import requests
import json
import time
import hashlib
import tempfile
import os
from playsound import playsound

def get_word_definition(word):
    url = f"https://api.dictionaryapi.dev/api/v2/entries/en/{word}"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            data = response.json()
            return parse_word_data(data)
        else:
            return f"查询失败: {response.status_code}"
    except Exception as e:
        return f"查询出错: {e}"

def parse_word_data(word_data):
    try:
        first_result = word_data[0]
        word = first_result.get('word', 'N/A')
        phonetic = first_result.get('phonetic', 'N/A')
        
        result = f"单词: {word}\n发音: {phonetic}\n\n"
        
        if 'meanings' in first_result:
            for meaning in first_result['meanings']:
                part_of_speech = meaning.get('partOfSpeech', 'N/A')
                result += f"词性: {part_of_speech}\n"
                
                for i, definition in enumerate(meaning.get('definitions', [])[:3]):
                    result += f"  {i+1}. {definition.get('definition', 'N/A')}\n"
                result += "\n"
        
        return result
    except Exception as e:
        return f"解析数据出错: {e}"

def translate_text(text, from_lang="en", to_lang="zh"):
    APP_ID = "WML1752072320905"
    APP_KEY = "571ed8d31b353b8069d40fb1ab96cee7"
    
    url = "https://api.niutrans.com/v2/text/translate"
    salt = str(int(time.time()))
    
    sign_raw = f"apikey={APP_KEY}&appId={APP_ID}&from={from_lang}&srcText={text}&timestamp={salt}&to={to_lang}"
    sign = hashlib.md5(sign_raw.encode('utf-8')).hexdigest()
    
    params = {
        "from": from_lang, "to": to_lang,
        "appId": APP_ID, "timestamp": salt,
        "authStr": sign, "srcText": text
    }
    
    try:
        response = requests.post(url, data=params)
        if response.status_code == 200:
            result = response.json()
            return result.get('tgtText', f"翻译结果解析失败: {result}")
        return f"翻译API请求失败: {response.status_code}"
    except Exception as e:
        return f"翻译API异常: {e}"

def text_to_speech(text):
    API_URL = "http://127.0.0.1:5000/synthesize"
    payload = {"text": text}
    
    try:
        response = requests.post(API_URL, json=payload, timeout=30)
        response.raise_for_status()
        
        if 'audio/wav' in response.headers.get('Content-Type', '').lower():
            wav_data = response.content
            
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
                tmp_file.write(wav_data)
                tmp_file_path = tmp_file.name
            
            print(f"播放音频: {text}")
            playsound(tmp_file_path)
            
            if os.path.exists(tmp_file_path):
                os.remove(tmp_file_path)
            
            return True
        else:
            print("API未返回有效音频文件")
            return False
            
    except Exception as e:
        print(f"TTS处理失败: {e}")
        return False

def smart_medicine_reader():
    print("=== 智能药品说明解读器 ===\n")
    
    spanish_text = "Vitamina C con Bioflavonoides y Escaramujo. Ingredientes por comprimido: Vitamina C (ácido ascórbico) 1000mg. Modo de empleo: Para adultos, tomar un (1) comprimido al día. Dosis diaria recomendada: 1 comprimido."
    
    print("原始西班牙语文本:")
    print(spanish_text)
    print("\n" + "="*50 + "\n")
    
    print("正在翻译...")
    chinese_text = translate_text(spanish_text, from_lang="es", to_lang="zh")
    print("翻译结果:")
    print(chinese_text)
    print("\n" + "="*50 + "\n")
    
    if isinstance(chinese_text, str) and "失败" not in chinese_text:
        key_info_list = []
        for sentence in chinese_text.replace('。', '.').split('.'):
            if '成分' in sentence or '剂量' in sentence:
                key_info_list.append(sentence.strip())
        
        key_info = "。".join(key_info_list)
        
        if key_info:
            print("提取的关键信息:")
            print(key_info)
            print("\n正在朗读关键信息...")
            text_to_speech(key_info)
        else:
            print("未能提取到关键信息")
    else:
        print("翻译失败，无法继续处理")

def demo_word_lookup():
    print("=== 词典查询演示 ===\n")
    
    word = "apple"
    print(f"查询单词: {word}")
    result = get_word_definition(word)
    print(result)
    print("="*50 + "\n")

def demo_translation():
    print("=== 翻译功能演示 ===\n")
    
    text = "Hello, how are you today?"
    print(f"原文: {text}")
    translation = translate_text(text)
    print(f"翻译: {translation}")
    print("="*50 + "\n")

def demo_tts():
    print("=== 文字转语音演示 ===\n")
    
    text = "你好，这是文字转语音测试。"
    print(f"朗读文本: {text}")
    success = text_to_speech(text)
    if success:
        print("朗读完成")
    else:
        print("朗读失败")
    print("="*50 + "\n")

def main():
    print("Python文本处理API演示程序")
    print("="*50)
    
    while True:
        print("\n请选择演示功能:")
        print("1. 词典查询")
        print("2. 文本翻译")
        print("3. 文字转语音")
        print("4. 智能药品说明解读器")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == "1":
            demo_word_lookup()
        elif choice == "2":
            demo_translation()
        elif choice == "3":
            demo_tts()
        elif choice == "4":
            smart_medicine_reader()
        elif choice == "0":
            print("再见！")
            break
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
