from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import Response
import pyttsx3
import tempfile
import os
from pydantic import BaseModel
import uvicorn

app = FastAPI(title="TTS Server", description="文字转语音服务器")

class TTSRequest(BaseModel):
    text: str

@app.post("/synthesize")
async def synthesize_text(request: TTSRequest):
    try:
        if not request.text.strip():
            raise HTTPException(status_code=400, detail="文本不能为空")
        
        engine = pyttsx3.init()
        
        voices = engine.getProperty('voices')
        for voice in voices:
            if 'zh' in voice.id.lower() or 'chinese' in voice.name.lower():
                engine.setProperty('voice', voice.id)
                break
        
        engine.setProperty('rate', 150)
        engine.setProperty('volume', 0.9)
        
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            temp_path = temp_file.name
        
        engine.save_to_file(request.text, temp_path)
        engine.runAndWait()
        
        if os.path.exists(temp_path):
            with open(temp_path, 'rb') as audio_file:
                audio_data = audio_file.read()
            
            os.unlink(temp_path)
            
            return Response(
                content=audio_data,
                media_type="audio/wav",
                headers={"Content-Disposition": "attachment; filename=speech.wav"}
            )
        else:
            raise HTTPException(status_code=500, detail="音频生成失败")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"TTS处理错误: {str(e)}")

@app.get("/health")
async def health_check():
    return {"status": "healthy", "message": "TTS服务器运行正常"}

if __name__ == "__main__":
    print("启动TTS服务器...")
    print("服务器地址: http://127.0.0.1:5000")
    print("健康检查: http://127.0.0.1:5000/health")
    print("按 Ctrl+C 停止服务器")
    
    uvicorn.run(app, host="127.0.0.1", port=5000)
