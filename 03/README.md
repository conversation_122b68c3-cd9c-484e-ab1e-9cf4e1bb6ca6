# Python文本处理API教学材料

基于 Notebook/L3.ipynb 的 Python文本处理API教学材料，适合小学生学习。

## 教学内容

本课程通过"智能文本处理"的方式教授：

1. **HTTP方法深化** - 理解GET和POST请求的区别与应用
2. **复杂JSON解析** - 处理多层嵌套的JSON数据结构
3. **API认证与签名** - 学习使用API密钥和MD5签名
4. **多媒体流处理** - 处理API返回的音频文件
5. **实践项目**：
   - 词典API：查询英语单词定义（GET请求）
   - 翻译API：小牛翻译服务（POST请求+签名）
   - 文字转语音API：TTS音频处理
6. **综合应用** - 智能药品说明解读器

## 文件夹结构

### teacher_demo/
老师课堂演示用的完整代码和服务器
- `text_processor_demo.py` - 完整的文本处理演示程序
- `tts_server.py` - TTS服务器（老师运行）

### answers/
标准答案文件
- `text_processor_complete.py` - 完整的参考答案

### templates/
学生练习模板（三种难度）
- `text_processor_easy.py` - 简单难度（填空题）
- `text_processor_medium.py` - 中等难度（提供骨架）
- `text_processor_hard.py` - 困难难度（仅框架）

### 辅助工具
- `auto_grader.py` - 自动测试打分工具
- `api_test_helper.py` - API连接测试工具

## 难度说明

### 简单难度（text_processor_easy.py）
- 几乎完整的程序代码
- 在关键位置设置填空
- 适合初学者理解API调用和JSON解析

### 中等难度（text_processor_medium.py）
- 提供函数框架和复杂逻辑
- 需要学生补充API调用和数据处理代码
- 适合有一定基础的学生

### 困难难度（text_processor_hard.py）
- 仅提供任务描述和API信息
- 需要学生独立完成所有代码
- 包含可选的高级功能：
  - 批量翻译功能
  - 语音合成缓存
  - 多语言支持
  - 错误重试机制
- 适合有经验的学生

## 使用说明

### 学生使用
1. 根据自己水平选择对应难度的模板文件
2. 获取小牛翻译API密钥（向老师申请）
3. 完成代码编写
4. 运行程序查看结果
5. 使用自动打分工具检验成果

### 老师使用
1. 运行 `teacher_demo/tts_server.py` 启动TTS服务器
2. 使用 `teacher_demo/` 中的代码进行课堂演示
3. 为学生提供小牛翻译API密钥
4. 参考 `answers/` 中的标准答案
5. 使用 `auto_grader.py` 对学生作业进行自动评分

### 测试工具
运行 `api_test_helper.py` 可以：
- 测试词典API连接状态
- 验证翻译API认证
- 检查TTS服务器状态
- 查看API返回数据格式

## 评分标准

自动打分工具评分项目：
- 导入库 (5分)
- 词典查询函数 (20分)
- JSON解析函数 (15分)
- 翻译函数（含签名） (25分)
- TTS音频处理函数 (20分)
- 综合应用函数 (10分)
- 代码质量和奖励功能 (5分)

## 技术要点

### API认证
- 学习MD5签名算法
- 理解时间戳的作用
- 掌握POST请求参数传递

### JSON处理
- 多层嵌套数据解析
- 安全的字典访问方法
- 错误处理机制

### 音频处理
- 二进制数据处理
- 临时文件管理
- 音频播放库使用

## 依赖库

学生需要安装：
```bash
pip install requests playsound==1.2.2
```

老师需要额外安装（用于TTS服务器）：
```bash
pip install fastapi uvicorn pyttsx3
```
