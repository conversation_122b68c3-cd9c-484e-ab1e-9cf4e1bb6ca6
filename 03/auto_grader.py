import importlib.util
import sys
import io
import contextlib
import traceback
import requests
import json
import time
import hashlib
from unittest.mock import patch, MagicMock

class TextProcessorGrader:
    def __init__(self, student_file_path):
        self.student_file_path = student_file_path
        self.student_module = None
        self.score = 0
        self.max_score = 100
        self.feedback = []
        
    def load_student_code(self):
        try:
            spec = importlib.util.spec_from_file_location("student_code", self.student_file_path)
            self.student_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(self.student_module)
            return True
        except Exception as e:
            self.feedback.append(f"代码加载失败: {str(e)}")
            return False
    
    def test_imports(self):
        try:
            required_modules = ['requests', 'json', 'time', 'hashlib']
            imported_count = 0
            
            for module in required_modules:
                if hasattr(self.student_module, module) or module in str(self.student_module.__dict__):
                    imported_count += 1
            
            if imported_count >= 3:
                self.score += 5
                self.feedback.append(f"✓ 正确导入必需库 (+5分)")
            else:
                self.feedback.append(f"✗ 缺少必需库导入 (需要: {required_modules})")
        except Exception as e:
            self.feedback.append(f"✗ 导入测试失败: {str(e)}")
    
    def test_get_word_definition(self):
        if not hasattr(self.student_module, 'get_word_definition'):
            self.feedback.append("✗ 缺少get_word_definition函数")
            return
        
        try:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = [{
                'word': 'test',
                'phonetic': '/test/',
                'meanings': [{
                    'partOfSpeech': 'noun',
                    'definitions': [{'definition': 'a test definition'}]
                }]
            }]
            
            with patch('requests.get', return_value=mock_response):
                result = self.student_module.get_word_definition('test')
                
                if isinstance(result, str) and 'test' in result.lower():
                    self.score += 20
                    self.feedback.append("✓ get_word_definition函数实现正确 (+20分)")
                else:
                    self.score += 10
                    self.feedback.append("△ get_word_definition函数部分正确 (+10分)")
                    
        except Exception as e:
            self.feedback.append(f"✗ get_word_definition函数测试失败: {str(e)}")
    
    def test_parse_word_data(self):
        if not hasattr(self.student_module, 'parse_word_data'):
            self.feedback.append("✗ 缺少parse_word_data函数")
            return
        
        try:
            test_data = [{
                'word': 'apple',
                'phonetic': '/ˈæp.əl/',
                'meanings': [{
                    'partOfSpeech': 'noun',
                    'definitions': [
                        {'definition': 'A round fruit'},
                        {'definition': 'A tree fruit'}
                    ]
                }]
            }]
            
            result = self.student_module.parse_word_data(test_data)
            
            if isinstance(result, str) and all(word in result.lower() for word in ['apple', 'noun']):
                self.score += 15
                self.feedback.append("✓ parse_word_data函数实现正确 (+15分)")
            else:
                self.score += 8
                self.feedback.append("△ parse_word_data函数部分正确 (+8分)")
                
        except Exception as e:
            self.feedback.append(f"✗ parse_word_data函数测试失败: {str(e)}")
    
    def test_translate_text(self):
        if not hasattr(self.student_module, 'translate_text'):
            self.feedback.append("✗ 缺少translate_text函数")
            return
        
        try:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {'tgtText': '你好'}
            
            with patch('requests.post', return_value=mock_response):
                result = self.student_module.translate_text('hello')
                
                if isinstance(result, str):
                    if '你好' in result or 'hello' in result.lower():
                        self.score += 25
                        self.feedback.append("✓ translate_text函数实现正确 (+25分)")
                    elif '演示模式' in result or 'api' in result.lower():
                        self.score += 15
                        self.feedback.append("△ translate_text函数结构正确，需配置API密钥 (+15分)")
                    else:
                        self.score += 10
                        self.feedback.append("△ translate_text函数部分正确 (+10分)")
                else:
                    self.score += 5
                    self.feedback.append("△ translate_text函数基本结构正确 (+5分)")
                    
        except Exception as e:
            self.feedback.append(f"✗ translate_text函数测试失败: {str(e)}")
    
    def test_text_to_speech(self):
        if not hasattr(self.student_module, 'text_to_speech'):
            self.feedback.append("✗ 缺少text_to_speech函数")
            return
        
        try:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.headers = {'Content-Type': 'audio/wav'}
            mock_response.content = b'fake_audio_data'
            
            with patch('requests.post', return_value=mock_response), \
                 patch('tempfile.NamedTemporaryFile'), \
                 patch('playsound.playsound'), \
                 patch('os.path.exists', return_value=True), \
                 patch('os.remove'):
                
                result = self.student_module.text_to_speech('test')
                
                if result is True:
                    self.score += 20
                    self.feedback.append("✓ text_to_speech函数实现正确 (+20分)")
                elif result is False:
                    self.score += 15
                    self.feedback.append("△ text_to_speech函数结构正确 (+15分)")
                else:
                    self.score += 10
                    self.feedback.append("△ text_to_speech函数部分正确 (+10分)")
                    
        except Exception as e:
            self.feedback.append(f"✗ text_to_speech函数测试失败: {str(e)}")
    
    def test_smart_medicine_reader(self):
        if not hasattr(self.student_module, 'smart_medicine_reader'):
            self.feedback.append("✗ 缺少smart_medicine_reader函数")
            return
        
        try:
            with patch.object(self.student_module, 'translate_text', return_value='维生素C。每片成分：维生素C 1000毫克。每日推荐剂量：1片'), \
                 patch.object(self.student_module, 'text_to_speech', return_value=True), \
                 patch('builtins.print'):
                
                self.student_module.smart_medicine_reader()
                self.score += 10
                self.feedback.append("✓ smart_medicine_reader函数运行成功 (+10分)")
                
        except Exception as e:
            self.feedback.append(f"✗ smart_medicine_reader函数测试失败: {str(e)}")
    
    def test_code_quality(self):
        try:
            with open(self.student_file_path, 'r', encoding='utf-8') as f:
                code_content = f.read()
            
            quality_score = 0
            
            if 'def ' in code_content:
                quality_score += 1
            if 'try:' in code_content and 'except' in code_content:
                quality_score += 1
            if 'if __name__ == "__main__"' in code_content:
                quality_score += 1
            if len(code_content.split('\n')) > 50:
                quality_score += 1
            if 'print(' in code_content:
                quality_score += 1
            
            self.score += quality_score
            self.feedback.append(f"✓ 代码质量评分: {quality_score}/5 (+{quality_score}分)")
            
        except Exception as e:
            self.feedback.append(f"✗ 代码质量测试失败: {str(e)}")
    
    def run_all_tests(self):
        print(f"开始评测文件: {self.student_file_path}")
        print("="*60)
        
        if not self.load_student_code():
            return self.generate_report()
        
        self.test_imports()
        self.test_get_word_definition()
        self.test_parse_word_data()
        self.test_translate_text()
        self.test_text_to_speech()
        self.test_smart_medicine_reader()
        self.test_code_quality()
        
        return self.generate_report()
    
    def generate_report(self):
        print("\n" + "="*60)
        print("评测报告")
        print("="*60)
        
        for feedback in self.feedback:
            print(feedback)
        
        print(f"\n总分: {self.score}/{self.max_score}")
        
        if self.score >= 90:
            grade = "优秀"
        elif self.score >= 80:
            grade = "良好"
        elif self.score >= 70:
            grade = "中等"
        elif self.score >= 60:
            grade = "及格"
        else:
            grade = "需要改进"
        
        print(f"等级: {grade}")
        print("="*60)
        
        return self.score, grade

def main():
    if len(sys.argv) != 2:
        print("使用方法: python auto_grader.py <学生代码文件路径>")
        print("示例: python auto_grader.py templates/text_processor_easy.py")
        return
    
    student_file = sys.argv[1]
    grader = TextProcessorGrader(student_file)
    grader.run_all_tests()

if __name__ == "__main__":
    main()
