import requests
import json
import time
import hashlib

def test_dictionary_api():
    print("=== 测试词典API ===")
    
    word = "test"
    url = f"https://api.dictionaryapi.dev/api/v2/entries/en/{word}"
    
    try:
        response = requests.get(url, timeout=10)
        print(f"请求URL: {url}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✓ 词典API连接成功")
            print(f"返回数据类型: {type(data)}")
            print(f"数据长度: {len(data) if isinstance(data, list) else 'N/A'}")
            
            if isinstance(data, list) and len(data) > 0:
                first_result = data[0]
                print(f"单词: {first_result.get('word', 'N/A')}")
                print(f"发音: {first_result.get('phonetic', 'N/A')}")
                print(f"词性数量: {len(first_result.get('meanings', []))}")
            
            print("\n示例JSON结构:")
            print(json.dumps(data[0] if isinstance(data, list) else data, indent=2, ensure_ascii=False)[:500] + "...")
        else:
            print(f"✗ 词典API请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"✗ 词典API测试异常: {e}")
    
    print("="*50)

def test_translation_api():
    print("=== 测试翻译API ===")
    
    APP_ID = "你的APP_ID"
    APP_KEY = "你的APP_KEY"
    
    if APP_ID.startswith("你的") or APP_KEY.startswith("你的"):
        print("⚠ 请先配置APP_ID和APP_KEY")
        print("获取方式: 访问 https://niutrans.com 注册账号")
        print("="*50)
        return
    
    url = "https://api.niutrans.com/v2/text/translate"
    text = "Hello"
    from_lang = "en"
    to_lang = "zh"
    
    try:
        salt = str(int(time.time()))
        sign_raw = f"apikey={APP_KEY}&appId={APP_ID}&from={from_lang}&srcText={text}&timestamp={salt}&to={to_lang}"
        sign = hashlib.md5(sign_raw.encode('utf-8')).hexdigest()
        
        params = {
            "from": from_lang, "to": to_lang,
            "appId": APP_ID, "timestamp": salt,
            "authStr": sign, "srcText": text
        }
        
        print(f"请求URL: {url}")
        print(f"签名字符串: {sign_raw}")
        print(f"MD5签名: {sign}")
        
        response = requests.post(url, data=params, timeout=10)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✓ 翻译API连接成功")
            print(f"原文: {text}")
            print(f"译文: {result.get('tgtText', 'N/A')}")
            print(f"完整响应: {result}")
        else:
            print(f"✗ 翻译API请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"✗ 翻译API测试异常: {e}")
    
    print("="*50)

def test_tts_server():
    print("=== 测试TTS服务器 ===")
    
    health_url = "http://127.0.0.1:5000/health"
    tts_url = "http://127.0.0.1:5000/synthesize"
    
    try:
        print(f"健康检查URL: {health_url}")
        health_response = requests.get(health_url, timeout=5)
        print(f"健康检查状态码: {health_response.status_code}")
        
        if health_response.status_code == 200:
            print("✓ TTS服务器运行正常")
            health_data = health_response.json()
            print(f"服务器状态: {health_data}")
            
            print(f"\n测试TTS功能...")
            print(f"TTS URL: {tts_url}")
            
            payload = {"text": "测试"}
            tts_response = requests.post(tts_url, json=payload, timeout=10)
            print(f"TTS响应状态码: {tts_response.status_code}")
            
            if tts_response.status_code == 200:
                content_type = tts_response.headers.get('Content-Type', '')
                content_length = len(tts_response.content)
                print(f"✓ TTS功能正常")
                print(f"响应类型: {content_type}")
                print(f"音频数据大小: {content_length} 字节")
            else:
                print(f"✗ TTS功能异常: {tts_response.status_code}")
                print(f"错误信息: {tts_response.text}")
        else:
            print(f"✗ TTS服务器健康检查失败: {health_response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到TTS服务器")
        print("请确保已启动TTS服务器: python teacher_demo/tts_server.py")
    except Exception as e:
        print(f"✗ TTS服务器测试异常: {e}")
    
    print("="*50)

def test_all_apis():
    print("Python文本处理API连接测试工具")
    print("="*50)
    
    test_dictionary_api()
    test_translation_api()
    test_tts_server()
    
    print("\n测试完成！")
    print("\n使用说明:")
    print("1. 词典API无需配置，可直接使用")
    print("2. 翻译API需要在代码中配置APP_ID和APP_KEY")
    print("3. TTS服务器需要先运行 teacher_demo/tts_server.py")
    print("4. 确保已安装所需库: pip install requests playsound==1.2.2")

def main():
    while True:
        print("\n请选择测试项目:")
        print("1. 测试词典API")
        print("2. 测试翻译API")
        print("3. 测试TTS服务器")
        print("4. 测试所有API")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == "1":
            test_dictionary_api()
        elif choice == "2":
            test_translation_api()
        elif choice == "3":
            test_tts_server()
        elif choice == "4":
            test_all_apis()
        elif choice == "0":
            print("再见！")
            break
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
