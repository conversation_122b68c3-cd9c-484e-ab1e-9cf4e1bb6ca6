import requests
import json
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

def get_weather(city):
    url = f"https://goweather.xyz/weather/{city}"
    try:
        response = requests.get(url)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"获取 {city} 天气失败: {e}")
        return None

def clean_temperature_data(temp_str):
    if not temp_str:
        return 0
    try:
        temp_value = int(temp_str.split()[0])
        return temp_value
    except (ValueError, IndexError):
        return 0

def clean_wind_data(wind_str):
    if not wind_str:
        return 0
    try:
        wind_value = int(wind_str.split()[0])
        return wind_value
    except (ValueError, IndexError):
        return 0

def get_clothing_advice(temp, wind):
    if temp < 5:
        base_advice = "天气寒冷，建议穿厚羽绒服、毛衣、保暖内衣。"
    elif 5 <= temp < 15:
        base_advice = "天气凉爽，建议穿外套、长袖衫、长裤。"
    elif 15 <= temp < 25:
        base_advice = "天气温和，建议穿薄外套或针织衫、T恤。"
    else:
        base_advice = "天气炎热，建议穿T恤、短裤，别忘了防晒！"
    
    wind_advice = ""
    if wind >= 36:
        wind_advice = "风力强劲，务必穿着防风外套，并注意保护头部和颈部。"
    elif wind >= 18:
        wind_advice = "有明显风感，建议选择一件防风的外套。"
    
    final_advice = (base_advice + " " + wind_advice).strip()
    return final_advice

def query_multiple_cities():
    cities = ['Beijing', 'Shanghai', 'Tokyo', 'London']
    print("--- 多城市天气速览 ---")
    
    for city in cities:
        weather_data = get_weather(city)
        if weather_data:
            temperature = weather_data.get('temperature', 'N/A')
            description = weather_data.get('description', 'N/A')
            print(f"{city:<10} | 温度: {temperature:<8} | 天气: {description}")

def visualize_forecast(city):
    weather_data = get_weather(city)
    
    if not weather_data or 'forecast' not in weather_data:
        print("无法获取预报数据")
        return
    
    forecast = weather_data['forecast']
    days = []
    temps = []
    
    current_temp = clean_temperature_data(weather_data.get('temperature', '0'))
    days.append('今天')
    temps.append(current_temp)
    
    for day_data in forecast:
        if day_data.get('temperature'):
            day_num = day_data.get('day', '未知')
            temp = clean_temperature_data(day_data['temperature'])
            if temp > 0:
                days.append(f"第{day_num}天")
                temps.append(temp)
    
    if len(temps) > 1:
        plt.figure(figsize=(10, 6))
        plt.plot(days, temps, marker='o', linewidth=2, markersize=8)
        plt.title(f'{city.capitalize()} 未来天气趋势')
        plt.xlabel('日期')
        plt.ylabel('温度 (°C)')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()
    else:
        print("预报数据不足，无法绘制图表")

def main():
    city = input("请输入城市拼音 (例如 beijing): ")
    weather_data = get_weather(city)
    
    if weather_data:
        temp_str = weather_data.get('temperature', '0 °C')
        wind_str = weather_data.get('wind', '0 km/h')
        description = weather_data.get('description', '未知')
        
        temp_value = clean_temperature_data(temp_str)
        wind_value = clean_wind_data(wind_str)
        
        advice = get_clothing_advice(temp_value, wind_value)
        
        print(f"\n--- 天气报告 ---")
        print(f"城市: {city.capitalize()}")
        print(f"天气: {description}")
        print(f"温度: {temp_str}")
        print(f"风速: {wind_str}")
        print(f"穿衣建议: {advice}")
        
        print("\n--- 多城市天气速览 ---")
        query_multiple_cities()
        
        show_chart = input("\n是否查看天气趋势图？(y/n): ")
        if show_chart.lower() == 'y':
            visualize_forecast(city)
    else:
        print("获取天气数据失败")

if __name__ == "__main__":
    main()
