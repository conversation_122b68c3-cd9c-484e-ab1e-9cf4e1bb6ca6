# Python天气查询与智能建议工具

基于 Notebook/L5.ipynb 的 Python 天气API编程教学材料，适合小学生学习。

## 教学内容

本课程通过构建智能天气助手教授：

1. **天气API使用** - 使用goweather.xyz免费API获取实时天气
2. **数据清洗技术** - 从字符串中提取数字信息
3. **条件判断逻辑** - 根据温度和风速提供智能建议
4. **循环与函数** - 批量查询多个城市天气
5. **数据可视化** - 使用matplotlib绘制天气趋势图

## 文件夹结构

### teacher_demo/
老师课堂演示用的完整代码
- `weather_assistant_demo.py` - 完整的天气助手演示程序
- `weather_server.py` - 本地天气服务器（用于演示）

### answers/
标准答案文件
- `weather_assistant_complete.py` - 完整的参考答案

### templates/
学生练习模板（三种难度）
- `weather_assistant_easy.py` - 简单难度（填空题）
- `weather_assistant_medium.py` - 中等难度（提供骨架）
- `weather_assistant_hard.py` - 困难难度（仅框架）

### 辅助工具
- `auto_grader.py` - 自动测试打分工具
- `api_test_helper.py` - 天气API连接测试工具

## 难度说明

### 简单难度（weather_assistant_easy.py）
- 几乎完整的程序代码
- 在关键位置设置填空
- 适合初学者理解API调用和数据处理流程

### 中等难度（weather_assistant_medium.py）
- 提供函数框架和复杂逻辑
- 需要学生补充数据处理和判断逻辑
- 适合有一定基础的学生

### 困难难度（weather_assistant_hard.py）
- 仅提供任务描述和API信息
- 需要学生独立完成所有功能
- 包含可选的高级功能（历史数据、多语言支持等）
- 适合有经验的学生

## 使用说明

### 学生使用
1. 根据自己水平选择对应难度的模板文件
2. 完成代码编写
3. 运行程序查看结果
4. 使用自动打分工具检验成果

### 老师使用
1. 使用 `teacher_demo/` 中的代码进行课堂演示
2. 参考 `answers/` 中的标准答案
3. 使用 `auto_grader.py` 对学生作业进行自动评分

### 测试工具
运行 `api_test_helper.py` 可以：
- 测试天气API的连接状态
- 查看API返回数据格式
- 验证完整的调用流程

## 评分标准

自动打分工具评分项目：
- 导入库 (5分)
- 天气数据获取函数 (20分)
- 数据清洗函数 (15分)
- 穿衣建议函数 (20分)
- 多城市查询功能 (15分)
- 数据可视化功能 (15分)
- 主函数和用户交互 (10分)

## API信息

使用的天气API：
- **API地址**: https://goweather.xyz/weather/{城市拼音}
- **请求方式**: GET
- **无需注册**: 免费使用
- **返回格式**: JSON
- **支持城市**: beijing, shanghai, tokyo, london等
