from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse

class WeatherHandler(BaseHTTPRequestHandler):
    
    weather_data = {
        'beijing': {
            'temperature': '+25 °C',
            'wind': '11 km/h',
            'description': 'Partly cloudy',
            'forecast': [
                {'day': '1', 'temperature': '+28 °C', 'wind': '15 km/h'},
                {'day': '2', 'temperature': '+30 °C', 'wind': '12 km/h'},
                {'day': '3', 'temperature': '+26 °C', 'wind': '8 km/h'}
            ]
        },
        'shanghai': {
            'temperature': '+29 °C',
            'wind': '8 km/h',
            'description': 'Clear',
            'forecast': [
                {'day': '1', 'temperature': '+32 °C', 'wind': '10 km/h'},
                {'day': '2', 'temperature': '+31 °C', 'wind': '9 km/h'},
                {'day': '3', 'temperature': '+28 °C', 'wind': '7 km/h'}
            ]
        },
        'tokyo': {
            'temperature': '+22 °C',
            'wind': '14 km/h',
            'description': 'Light rain',
            'forecast': [
                {'day': '1', 'temperature': '+24 °C', 'wind': '16 km/h'},
                {'day': '2', 'temperature': '+26 °C', 'wind': '12 km/h'},
                {'day': '3', 'temperature': '+23 °C', 'wind': '11 km/h'}
            ]
        },
        'london': {
            'temperature': '18 °C',
            'wind': '20 km/h',
            'description': 'Cloudy',
            'forecast': [
                {'day': '1', 'temperature': '20 °C', 'wind': '22 km/h'},
                {'day': '2', 'temperature': '19 °C', 'wind': '18 km/h'},
                {'day': '3', 'temperature': '17 °C', 'wind': '25 km/h'}
            ]
        }
    }
    
    def do_GET(self):
        parsed_path = urllib.parse.urlparse(self.path)
        path_parts = parsed_path.path.strip('/').split('/')
        
        if len(path_parts) >= 2 and path_parts[0] == 'weather':
            city = path_parts[1].lower()
            
            if city in self.weather_data:
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                response_data = self.weather_data[city]
                self.wfile.write(json.dumps(response_data).encode())
            else:
                self.send_response(404)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                error_response = {'error': 'City not found'}
                self.wfile.write(json.dumps(error_response).encode())
        else:
            self.send_response(404)
            self.send_header('Content-type', 'text/plain')
            self.end_headers()
            self.wfile.write(b'Not Found')
    
    def log_message(self, format, *args):
        print(f"[天气服务器] {format % args}")

def start_weather_server(port=8080):
    server_address = ('', port)
    httpd = HTTPServer(server_address, WeatherHandler)
    print(f"天气服务器启动在端口 {port}")
    print(f"测试地址: http://localhost:{port}/weather/beijing")
    print("按 Ctrl+C 停止服务器")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n服务器已停止")
        httpd.server_close()

if __name__ == "__main__":
    start_weather_server()
