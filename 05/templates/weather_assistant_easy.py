import ______
import json

def get_weather(city):
    url = f"https://goweather.xyz/weather/{______}"
    try:
        response = requests.______(url)
        response.raise_for_status()
        return response.______()
    except requests.exceptions.RequestException as e:
        print(f"获取 {city} 天气失败: {e}")
        return None

def clean_temperature_data(temp_str):
    if not temp_str:
        return 0
    try:
        temp_value = int(temp_str.______()[0])
        return temp_value
    except (ValueError, IndexError):
        return 0

def clean_wind_data(wind_str):
    if not wind_str:
        return 0
    try:
        wind_value = int(wind_str.split()[______])
        return wind_value
    except (ValueError, IndexError):
        return 0

def get_clothing_advice(temp, wind):
    if temp < ______:
        base_advice = "天气寒冷，建议穿厚羽绒服、毛衣、保暖内衣。"
    elif 5 <= temp < ______:
        base_advice = "天气凉爽，建议穿外套、长袖衫、长裤。"
    elif 15 <= temp < ______:
        base_advice = "天气温和，建议穿薄外套或针织衫、T恤。"
    else:
        base_advice = "天气炎热，建议穿T恤、短裤，别忘了防晒！"
    
    wind_advice = ""
    if wind >= 36:
        wind_advice = "风力强劲，务必穿着防风外套，并注意保护头部和颈部。"
    elif wind >= ______:
        wind_advice = "有明显风感，建议选择一件防风的外套。"
    
    final_advice = (base_advice + " " + wind_advice).______()
    return final_advice

def query_multiple_cities():
    cities = ['Beijing', 'Shanghai', 'Tokyo', 'London']
    print("--- 多城市天气速览 ---")
    
    for city in ______:
        weather_data = get_weather(city)
        if weather_data:
            temperature = weather_data.get('______', 'N/A')
            description = weather_data.get('______', 'N/A')
            print(f"{city:<10} | 温度: {temperature:<8} | 天气: {description}")

def main():
    city = input("请输入城市拼音 (例如 beijing): ")
    weather_data = get_weather(city)
    
    if weather_data:
        temp_str = weather_data.get('______', '0 °C')
        wind_str = weather_data.get('______', '0 km/h')
        description = weather_data.get('______', '未知')
        
        temp_value = clean_temperature_data(temp_str)
        wind_value = clean_wind_data(wind_str)
        
        advice = get_clothing_advice(temp_value, wind_value)
        
        print(f"\n--- 天气报告 ---")
        print(f"城市: {city.capitalize()}")
        print(f"天气: {description}")
        print(f"温度: {temp_str}")
        print(f"风速: {wind_str}")
        print(f"穿衣建议: {advice}")
        
        print("\n--- 多城市天气速览 ---")
        query_multiple_cities()
    else:
        print("获取天气数据失败")

if __name__ == "__main__":
    main()

# 填空提示：
# 1. 导入requests库
# 2. 在URL中填入城市变量
# 3. 发送GET请求
# 4. 获取JSON数据
# 5. 使用split()方法分割字符串
# 6. 获取分割后的第一个元素（索引0）
# 7. 温度判断的阈值：5, 15, 25
# 8. 风速判断的阈值：18
# 9. 使用strip()方法去除多余空格
# 10. 遍历城市列表
# 11. 从天气数据中获取温度、描述等信息
