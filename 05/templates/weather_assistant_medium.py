import requests
import json
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

def get_weather(city):
    """
    获取指定城市的天气数据
    参数: city - 城市名称（拼音）
    返回: 天气数据字典，失败时返回None
    """
    # TODO: 构建API请求URL
    # TODO: 发送GET请求并处理异常
    # TODO: 返回JSON数据
    pass

def clean_temperature_data(temp_str):
    """
    从温度字符串中提取数字
    参数: temp_str - 温度字符串，如 "+25 °C"
    返回: 温度数值（整数）
    """
    # TODO: 检查输入是否为空
    # TODO: 使用split()分割字符串
    # TODO: 转换为整数并处理异常
    pass

def clean_wind_data(wind_str):
    """
    从风速字符串中提取数字
    参数: wind_str - 风速字符串，如 "11 km/h"
    返回: 风速数值（整数）
    """
    # TODO: 实现与clean_temperature_data类似的逻辑
    pass

def get_clothing_advice(temp, wind):
    """
    根据温度和风速提供穿衣建议
    参数: temp - 温度值, wind - 风速值
    返回: 穿衣建议字符串
    """
    # TODO: 根据温度范围设置基础建议
    # 温度范围：< 5, 5-15, 15-25, >= 25
    
    # TODO: 根据风速添加防风建议
    # 风速范围：>= 36 (强风), >= 18 (有风感)
    
    # TODO: 组合建议并返回
    pass

def query_multiple_cities():
    """
    查询多个城市的天气概况
    """
    cities = ['Beijing', 'Shanghai', 'Tokyo', 'London']
    print("--- 多城市天气速览 ---")
    
    # TODO: 遍历城市列表
    # TODO: 获取每个城市的天气数据
    # TODO: 格式化输出温度和天气描述
    pass

def visualize_forecast(city):
    """
    可视化天气预报趋势
    参数: city - 城市名称
    """
    # TODO: 获取天气数据
    # TODO: 提取当前温度和预报数据
    # TODO: 使用matplotlib绘制趋势图
    pass

def main():
    """
    主函数 - 程序入口
    """
    # TODO: 获取用户输入的城市名称
    # TODO: 获取天气数据
    # TODO: 清洗温度和风速数据
    # TODO: 生成穿衣建议
    # TODO: 格式化输出天气报告
    # TODO: 调用多城市查询功能
    # TODO: 询问是否显示趋势图
    pass

if __name__ == "__main__":
    main()

# 实现提示：
# 1. get_weather函数需要处理HTTP请求和异常
# 2. 数据清洗函数需要使用字符串的split()方法
# 3. 穿衣建议函数需要使用if-elif-else条件判断
# 4. 多城市查询需要使用for循环
# 5. 可视化功能需要使用matplotlib库
# 6. 主函数需要整合所有功能模块
