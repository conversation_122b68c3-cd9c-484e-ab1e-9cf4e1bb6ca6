# Python天气查询与智能建议工具
# 困难难度 - 独立完成所有功能

"""
项目要求：
构建一个智能天气助手，实现以下核心功能：

1. 基础功能（必须实现）：
   - 获取指定城市的实时天气数据
   - 从API返回的字符串中提取温度和风速数值
   - 根据温度和风速提供智能穿衣建议
   - 支持查询多个城市的天气概况
   - 可视化显示天气预报趋势

2. 高级功能（可选实现）：
   - 支持中英文城市名称输入
   - 添加天气预警功能（极端天气提醒）
   - 实现天气数据的本地缓存
   - 支持历史天气数据查询
   - 添加更详细的生活指数建议（紫外线、运动等）

API信息：
- 天气API地址：https://goweather.xyz/weather/{城市拼音}
- 请求方式：GET
- 返回格式：JSON
- 主要字段：temperature, wind, description, forecast

数据格式示例：
{
  "temperature": "+25 °C",
  "wind": "11 km/h", 
  "description": "Partly cloudy",
  "forecast": [
    {"day": "1", "temperature": "+28 °C", "wind": "15 km/h"},
    {"day": "2", "temperature": "+30 °C", "wind": "12 km/h"}
  ]
}

穿衣建议规则：
- 温度 < 5°C：厚羽绒服、毛衣、保暖内衣
- 5°C ≤ 温度 < 15°C：外套、长袖衫、长裤
- 15°C ≤ 温度 < 25°C：薄外套或针织衫、T恤
- 温度 ≥ 25°C：T恤、短裤，注意防晒

风速建议：
- 风速 ≥ 36 km/h：强风，需要防风外套和头部保护
- 18 km/h ≤ 风速 < 36 km/h：有风感，建议防风外套

需要的Python库：
- requests：用于HTTP请求
- json：用于数据解析
- matplotlib：用于数据可视化
- 其他根据需要自行选择

评分标准：
- 基础功能实现完整性 (60分)
- 代码结构和可读性 (20分)
- 异常处理和健壮性 (10分)
- 高级功能实现 (10分)

开始编程吧！记住要：
1. 合理组织代码结构，使用函数封装功能
2. 添加适当的异常处理
3. 考虑用户体验，提供清晰的提示信息
4. 测试各种边界情况
"""

# 在这里开始你的代码实现

if __name__ == "__main__":
    # 程序入口
    pass
