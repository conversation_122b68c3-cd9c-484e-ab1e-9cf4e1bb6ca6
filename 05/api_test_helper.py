import requests
import json
import time

class WeatherAPITester:
    def __init__(self):
        self.base_url = "https://goweather.xyz/weather"
        self.test_cities = ['beijing', 'shanghai', 'tokyo', 'london', 'newyork']
        
    def test_single_city(self, city):
        """测试单个城市的API调用"""
        print(f"\n测试城市: {city}")
        print("-" * 30)
        
        url = f"{self.base_url}/{city}"
        
        try:
            start_time = time.time()
            response = requests.get(url, timeout=10)
            end_time = time.time()
            
            print(f"请求URL: {url}")
            print(f"响应状态码: {response.status_code}")
            print(f"响应时间: {(end_time - start_time):.2f} 秒")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print("✓ JSON解析成功")
                    print("返回数据:")
                    print(json.dumps(data, indent=2, ensure_ascii=False))
                    
                    # 检查必要字段
                    required_fields = ['temperature', 'wind', 'description']
                    missing_fields = []
                    
                    for field in required_fields:
                        if field not in data:
                            missing_fields.append(field)
                    
                    if missing_fields:
                        print(f"⚠ 缺少字段: {', '.join(missing_fields)}")
                    else:
                        print("✓ 所有必要字段都存在")
                        
                    # 检查预报数据
                    if 'forecast' in data and isinstance(data['forecast'], list):
                        print(f"✓ 预报数据: {len(data['forecast'])} 天")
                    else:
                        print("⚠ 无预报数据或格式异常")
                        
                    return True
                    
                except json.JSONDecodeError:
                    print("✗ JSON解析失败")
                    print("原始响应:", response.text[:200])
                    return False
            else:
                print(f"✗ 请求失败，状态码: {response.status_code}")
                return False
                
        except requests.exceptions.Timeout:
            print("✗ 请求超时")
            return False
        except requests.exceptions.ConnectionError:
            print("✗ 连接错误")
            return False
        except Exception as e:
            print(f"✗ 其他错误: {e}")
            return False
    
    def test_all_cities(self):
        """测试所有预设城市"""
        print("=" * 50)
        print("天气API连接测试")
        print("=" * 50)
        
        success_count = 0
        total_count = len(self.test_cities)
        
        for city in self.test_cities:
            if self.test_single_city(city):
                success_count += 1
            time.sleep(1)  # 避免请求过于频繁
        
        print("\n" + "=" * 50)
        print("测试总结")
        print("=" * 50)
        print(f"成功: {success_count}/{total_count} 个城市")
        print(f"成功率: {(success_count/total_count)*100:.1f}%")
        
        if success_count == total_count:
            print("✓ 所有API测试通过")
        elif success_count > 0:
            print("△ 部分API可用")
        else:
            print("✗ 所有API测试失败")
    
    def test_data_parsing(self):
        """测试数据解析功能"""
        print("\n" + "=" * 50)
        print("数据解析测试")
        print("=" * 50)
        
        # 模拟API返回的数据格式
        test_data = {
            'temperature': '+25 °C',
            'wind': '11 km/h',
            'description': 'Partly cloudy',
            'forecast': [
                {'day': '1', 'temperature': '+28 °C', 'wind': '15 km/h'},
                {'day': '2', 'temperature': '+30 °C', 'wind': '12 km/h'}
            ]
        }
        
        print("测试数据:")
        print(json.dumps(test_data, indent=2, ensure_ascii=False))
        
        # 测试温度解析
        temp_str = test_data['temperature']
        try:
            temp_value = int(temp_str.split()[0])
            print(f"\n✓ 温度解析成功: '{temp_str}' -> {temp_value}")
        except:
            print(f"\n✗ 温度解析失败: '{temp_str}'")
        
        # 测试风速解析
        wind_str = test_data['wind']
        try:
            wind_value = int(wind_str.split()[0])
            print(f"✓ 风速解析成功: '{wind_str}' -> {wind_value}")
        except:
            print(f"✗ 风速解析失败: '{wind_str}'")
        
        # 测试预报数据解析
        forecast = test_data.get('forecast', [])
        print(f"✓ 预报数据: {len(forecast)} 天")
        
        for i, day_data in enumerate(forecast):
            day = day_data.get('day', '未知')
            temp = day_data.get('temperature', 'N/A')
            print(f"  第{day}天: {temp}")
    
    def interactive_test(self):
        """交互式测试"""
        print("\n" + "=" * 50)
        print("交互式API测试")
        print("=" * 50)
        
        while True:
            city = input("\n请输入要测试的城市名称（拼音，输入'quit'退出）: ").strip().lower()
            
            if city == 'quit':
                break
            
            if not city:
                print("请输入有效的城市名称")
                continue
            
            self.test_single_city(city)
    
    def run_all_tests(self):
        """运行所有测试"""
        self.test_all_cities()
        self.test_data_parsing()
        
        choice = input("\n是否进行交互式测试？(y/n): ").strip().lower()
        if choice == 'y':
            self.interactive_test()

def main():
    print("天气API测试工具")
    print("此工具用于测试goweather.xyz API的连接状态和数据格式")
    
    tester = WeatherAPITester()
    tester.run_all_tests()
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
