import importlib.util
import sys
import os
import inspect
from unittest.mock import patch, MagicMock

class WeatherAssistantGrader:
    def __init__(self, student_file_path):
        self.student_file_path = student_file_path
        self.student_module = None
        self.total_score = 0
        self.max_score = 100
        self.test_results = []
        
    def load_student_module(self):
        try:
            spec = importlib.util.spec_from_file_location("student_weather", self.student_file_path)
            self.student_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(self.student_module)
            return True
        except Exception as e:
            self.add_result("模块加载", 0, 5, f"无法加载学生代码: {e}")
            return False
    
    def add_result(self, test_name, score, max_score, message=""):
        self.test_results.append({
            'test': test_name,
            'score': score,
            'max_score': max_score,
            'message': message
        })
        self.total_score += score
    
    def test_imports(self):
        score = 0
        max_score = 5
        
        required_modules = ['requests']
        imported_modules = []
        
        try:
            source_code = open(self.student_file_path, 'r', encoding='utf-8').read()
            
            if 'import requests' in source_code or 'from requests' in source_code:
                imported_modules.append('requests')
                score += 3
            
            if 'import json' in source_code or 'from json' in source_code:
                score += 1
                
            if 'import matplotlib' in source_code or 'from matplotlib' in source_code:
                score += 1
                
        except Exception as e:
            pass
        
        message = f"导入的模块: {', '.join(imported_modules) if imported_modules else '无'}"
        self.add_result("导入库", score, max_score, message)
    
    def test_get_weather_function(self):
        score = 0
        max_score = 20
        
        if not hasattr(self.student_module, 'get_weather'):
            self.add_result("天气数据获取函数", 0, max_score, "未找到get_weather函数")
            return
        
        try:
            func = getattr(self.student_module, 'get_weather')
            
            # 检查函数签名
            sig = inspect.signature(func)
            if len(sig.parameters) >= 1:
                score += 5
            
            # 模拟API响应测试
            mock_response = MagicMock()
            mock_response.json.return_value = {
                'temperature': '+25 °C',
                'wind': '11 km/h',
                'description': 'Clear'
            }
            mock_response.raise_for_status.return_value = None
            
            with patch('requests.get', return_value=mock_response):
                result = func('beijing')
                if isinstance(result, dict) and 'temperature' in result:
                    score += 15
                    
        except Exception as e:
            message = f"函数执行错误: {str(e)[:100]}"
            
        self.add_result("天气数据获取函数", score, max_score, 
                       f"函数实现完整性: {score}/{max_score}")
    
    def test_data_cleaning_functions(self):
        score = 0
        max_score = 15
        
        # 测试温度数据清洗
        if hasattr(self.student_module, 'clean_temperature_data'):
            try:
                func = getattr(self.student_module, 'clean_temperature_data')
                
                test_cases = [
                    ('+25 °C', 25),
                    ('18 °C', 18),
                    ('-5 °C', -5),
                    ('', 0),
                    (None, 0)
                ]
                
                correct_count = 0
                for input_val, expected in test_cases:
                    try:
                        result = func(input_val)
                        if result == expected:
                            correct_count += 1
                    except:
                        pass
                
                if correct_count >= 3:
                    score += 8
                elif correct_count >= 1:
                    score += 4
                    
            except Exception:
                pass
        
        # 测试风速数据清洗
        if hasattr(self.student_module, 'clean_wind_data'):
            try:
                func = getattr(self.student_module, 'clean_wind_data')
                
                test_cases = [
                    ('11 km/h', 11),
                    ('25 km/h', 25),
                    ('', 0)
                ]
                
                correct_count = 0
                for input_val, expected in test_cases:
                    try:
                        result = func(input_val)
                        if result == expected:
                            correct_count += 1
                    except:
                        pass
                
                if correct_count >= 2:
                    score += 7
                elif correct_count >= 1:
                    score += 3
                    
            except Exception:
                pass
        
        self.add_result("数据清洗函数", score, max_score, 
                       f"数据处理准确性: {score}/{max_score}")
    
    def test_clothing_advice_function(self):
        score = 0
        max_score = 20
        
        if not hasattr(self.student_module, 'get_clothing_advice'):
            self.add_result("穿衣建议函数", 0, max_score, "未找到get_clothing_advice函数")
            return
        
        try:
            func = getattr(self.student_module, 'get_clothing_advice')
            
            test_cases = [
                (0, 10, "寒冷"),    # 低温
                (10, 10, "凉爽"),   # 中低温
                (20, 10, "温和"),   # 中温
                (30, 10, "炎热"),   # 高温
                (20, 40, "防风")    # 大风
            ]
            
            correct_count = 0
            for temp, wind, expected_keyword in test_cases:
                try:
                    result = func(temp, wind)
                    if isinstance(result, str) and len(result) > 0:
                        correct_count += 1
                        if expected_keyword in result or any(keyword in result for keyword in ["寒冷", "凉爽", "温和", "炎热", "防风"]):
                            score += 3
                except:
                    pass
            
            if correct_count >= 4:
                score += 5
                
        except Exception as e:
            pass
        
        self.add_result("穿衣建议函数", score, max_score, 
                       f"建议逻辑准确性: {score}/{max_score}")
    
    def test_multiple_cities_function(self):
        score = 0
        max_score = 15
        
        if hasattr(self.student_module, 'query_multiple_cities'):
            score += 5
            
            # 检查是否使用了循环
            try:
                source_code = open(self.student_file_path, 'r', encoding='utf-8').read()
                if 'for' in source_code and 'cities' in source_code:
                    score += 10
            except:
                pass
        
        self.add_result("多城市查询功能", score, max_score, 
                       f"功能实现: {score}/{max_score}")
    
    def test_visualization_function(self):
        score = 0
        max_score = 15
        
        if hasattr(self.student_module, 'visualize_forecast'):
            score += 5
            
            # 检查是否使用了matplotlib
            try:
                source_code = open(self.student_file_path, 'r', encoding='utf-8').read()
                if 'plt.' in source_code or 'matplotlib' in source_code:
                    score += 10
            except:
                pass
        
        self.add_result("数据可视化功能", score, max_score, 
                       f"可视化实现: {score}/{max_score}")
    
    def test_main_function(self):
        score = 0
        max_score = 10
        
        if hasattr(self.student_module, 'main'):
            score += 5
            
            # 检查主函数调用
            try:
                source_code = open(self.student_file_path, 'r', encoding='utf-8').read()
                if '__name__ == "__main__"' in source_code:
                    score += 5
            except:
                pass
        
        self.add_result("主函数和用户交互", score, max_score, 
                       f"程序结构: {score}/{max_score}")
    
    def run_all_tests(self):
        print("开始自动评分...")
        print("=" * 50)
        
        if not self.load_student_module():
            return
        
        self.test_imports()
        self.test_get_weather_function()
        self.test_data_cleaning_functions()
        self.test_clothing_advice_function()
        self.test_multiple_cities_function()
        self.test_visualization_function()
        self.test_main_function()
        
        self.print_results()
    
    def print_results(self):
        print("\n评分结果:")
        print("=" * 50)
        
        for result in self.test_results:
            status = "✓" if result['score'] == result['max_score'] else "✗" if result['score'] == 0 else "△"
            print(f"{status} {result['test']}: {result['score']}/{result['max_score']} 分")
            if result['message']:
                print(f"   {result['message']}")
        
        print("=" * 50)
        print(f"总分: {self.total_score}/{self.max_score} 分")
        
        percentage = (self.total_score / self.max_score) * 100
        if percentage >= 90:
            grade = "优秀"
        elif percentage >= 80:
            grade = "良好"
        elif percentage >= 70:
            grade = "中等"
        elif percentage >= 60:
            grade = "及格"
        else:
            grade = "需要改进"
        
        print(f"评级: {grade} ({percentage:.1f}%)")

def main():
    if len(sys.argv) != 2:
        print("使用方法: python auto_grader.py <学生代码文件路径>")
        print("示例: python auto_grader.py templates/weather_assistant_easy.py")
        return
    
    student_file = sys.argv[1]
    
    if not os.path.exists(student_file):
        print(f"错误: 文件 {student_file} 不存在")
        return
    
    grader = WeatherAssistantGrader(student_file)
    grader.run_all_tests()

if __name__ == "__main__":
    main()
