import os
import sys
import importlib.util
import requests
from PIL import Image
import tempfile
import shutil

class ImageProcessorGrader:
    """图像处理程序自动评分器"""
    
    def __init__(self):
        self.total_score = 0
        self.max_score = 100
        self.test_results = []
        self.temp_dir = None
    
    def setup_test_environment(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        print(f"创建临时测试目录: {self.temp_dir}")
    
    def cleanup_test_environment(self):
        """清理测试环境"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            print(f"清理临时目录: {self.temp_dir}")
    
    def load_student_module(self, file_path):
        """加载学生代码模块"""
        try:
            spec = importlib.util.spec_from_file_location("student_code", file_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            return module
        except Exception as e:
            print(f"加载学生代码失败: {e}")
            return None
    
    def test_imports(self, module):
        """测试导入库 (5分)"""
        score = 0
        required_imports = ['requests', 'os', 'PIL']
        
        print("\n测试1: 导入库检查")
        print("-" * 20)
        
        for imp in required_imports:
            if hasattr(module, imp) or imp in sys.modules:
                print(f"✓ {imp} 导入成功")
                score += 1.67
            else:
                print(f"✗ {imp} 未导入")
        
        score = min(score, 5)
        self.add_score(score, 5, "导入库")
        return score > 0
    
    def test_download_function(self, module):
        """测试图像下载函数 (25分)"""
        score = 0
        print("\n测试2: 图像下载功能")
        print("-" * 20)
        
        # 检查函数存在
        if hasattr(module, 'download_image'):
            print("✓ download_image 函数存在")
            score += 5
            
            try:
                # 测试下载功能
                test_url = "https://picsum.photos/200/200"
                test_file = os.path.join(self.temp_dir, "test_download.jpg")
                
                result = module.download_image(test_url, test_file)
                
                if result and os.path.exists(test_file):
                    print("✓ 下载功能正常")
                    score += 10
                    
                    # 检查文件大小
                    file_size = os.path.getsize(test_file)
                    if file_size > 0:
                        print(f"✓ 文件大小正常: {file_size} 字节")
                        score += 5
                    
                    # 检查是否为有效图像
                    try:
                        with Image.open(test_file) as img:
                            print(f"✓ 图像有效: {img.format} {img.size}")
                            score += 5
                    except:
                        print("✗ 图像文件无效")
                else:
                    print("✗ 下载功能失败")
                    
            except Exception as e:
                print(f"✗ 下载测试异常: {e}")
        else:
            print("✗ download_image 函数不存在")
        
        self.add_score(score, 25, "图像下载功能")
        return score > 15
    
    def test_chunked_download(self, module):
        """测试分块下载实现 (20分)"""
        score = 0
        print("\n测试3: 分块下载功能")
        print("-" * 20)
        
        # 检查是否使用了stream=True和iter_content
        try:
            import inspect
            if hasattr(module, 'download_image'):
                source = inspect.getsource(module.download_image)
                
                if 'stream=True' in source or 'stream = True' in source:
                    print("✓ 使用了stream=True")
                    score += 8
                else:
                    print("✗ 未使用stream=True")
                
                if 'iter_content' in source:
                    print("✓ 使用了iter_content")
                    score += 8
                else:
                    print("✗ 未使用iter_content")
                
                if 'chunk' in source:
                    print("✓ 实现了分块处理")
                    score += 4
                else:
                    print("✗ 未实现分块处理")
            
        except Exception as e:
            print(f"代码分析失败: {e}")
        
        self.add_score(score, 20, "分块下载实现")
        return score > 10
    
    def test_file_operations(self, module):
        """测试文件保存功能 (15分)"""
        score = 0
        print("\n测试4: 文件操作功能")
        print("-" * 20)
        
        try:
            import inspect
            if hasattr(module, 'download_image'):
                source = inspect.getsource(module.download_image)
                
                if "'wb'" in source or '"wb"' in source:
                    print("✓ 使用了正确的二进制写入模式")
                    score += 8
                else:
                    print("✗ 未使用'wb'模式")
                
                if 'with open' in source:
                    print("✓ 使用了with语句")
                    score += 7
                else:
                    print("✗ 未使用with语句")
        
        except Exception as e:
            print(f"文件操作检查失败: {e}")
        
        self.add_score(score, 15, "文件保存功能")
        return score > 8
    
    def test_error_handling(self, module):
        """测试异常处理 (15分)"""
        score = 0
        print("\n测试5: 异常处理")
        print("-" * 20)
        
        try:
            import inspect
            if hasattr(module, 'download_image'):
                source = inspect.getsource(module.download_image)
                
                if 'try:' in source and 'except' in source:
                    print("✓ 实现了异常处理")
                    score += 8
                else:
                    print("✗ 未实现异常处理")
                
                if 'RequestException' in source or 'Exception' in source:
                    print("✓ 处理了请求异常")
                    score += 7
                else:
                    print("✗ 未处理请求异常")
        
        except Exception as e:
            print(f"异常处理检查失败: {e}")
        
        self.add_score(score, 15, "异常处理")
        return score > 8
    
    def test_image_info_function(self, module):
        """测试图像信息获取功能 (10分)"""
        score = 0
        print("\n测试6: 图像信息功能")
        print("-" * 20)
        
        if hasattr(module, 'get_image_info'):
            print("✓ get_image_info 函数存在")
            score += 5
            
            # 创建测试图像
            test_file = os.path.join(self.temp_dir, "test_info.jpg")
            try:
                # 下载测试图像
                response = requests.get("https://picsum.photos/100/100")
                with open(test_file, 'wb') as f:
                    f.write(response.content)
                
                # 测试函数
                result = module.get_image_info(test_file)
                if result:
                    print("✓ 图像信息获取成功")
                    score += 5
                
            except Exception as e:
                print(f"图像信息测试失败: {e}")
        else:
            print("✗ get_image_info 函数不存在")
        
        self.add_score(score, 10, "图像信息功能")
        return score > 5
    
    def test_bonus_features(self, module):
        """测试奖励功能 (10分)"""
        score = 0
        print("\n测试7: 奖励功能")
        print("-" * 20)
        
        bonus_functions = [
            ('read_exif_data', 'EXIF读取'),
            ('verify_image_integrity', '完整性验证'),
            ('batch_download_images', '批量下载'),
            ('calculate_file_hash', '哈希计算')
        ]
        
        for func_name, desc in bonus_functions:
            if hasattr(module, func_name):
                print(f"✓ {desc} 功能存在")
                score += 2.5
            else:
                print(f"- {desc} 功能未实现")
        
        self.add_score(score, 10, "奖励功能")
        return score > 0
    
    def add_score(self, earned, maximum, category):
        """添加分数"""
        self.total_score += earned
        self.test_results.append({
            'category': category,
            'earned': earned,
            'maximum': maximum,
            'percentage': (earned / maximum) * 100 if maximum > 0 else 0
        })
        print(f"得分: {earned:.1f}/{maximum} ({(earned/maximum)*100:.1f}%)")
    
    def generate_report(self):
        """生成评分报告"""
        print("\n" + "=" * 50)
        print("评分报告")
        print("=" * 50)
        
        for result in self.test_results:
            print(f"{result['category']:<15}: {result['earned']:>5.1f}/{result['maximum']:<3} "
                  f"({result['percentage']:>5.1f}%)")
        
        print("-" * 50)
        print(f"总分: {self.total_score:.1f}/{self.max_score} ({(self.total_score/self.max_score)*100:.1f}%)")
        
        # 等级评定
        percentage = (self.total_score / self.max_score) * 100
        if percentage >= 90:
            grade = "优秀"
        elif percentage >= 80:
            grade = "良好"
        elif percentage >= 70:
            grade = "中等"
        elif percentage >= 60:
            grade = "及格"
        else:
            grade = "不及格"
        
        print(f"等级: {grade}")
    
    def grade_file(self, file_path):
        """评分指定文件"""
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return
        
        print(f"开始评分: {file_path}")
        print("=" * 50)
        
        self.setup_test_environment()
        
        try:
            module = self.load_student_module(file_path)
            if not module:
                print("无法加载学生代码")
                return
            
            # 运行所有测试
            self.test_imports(module)
            self.test_download_function(module)
            self.test_chunked_download(module)
            self.test_file_operations(module)
            self.test_error_handling(module)
            self.test_image_info_function(module)
            self.test_bonus_features(module)
            
            self.generate_report()
            
        finally:
            self.cleanup_test_environment()

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python auto_grader.py <学生代码文件>")
        print("示例: python auto_grader.py templates/image_processor_easy.py")
        return
    
    file_path = sys.argv[1]
    grader = ImageProcessorGrader()
    grader.grade_file(file_path)

if __name__ == "__main__":
    main()
