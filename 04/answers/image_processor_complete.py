import requests
import os
from PIL import Image
from PIL.ExifTags import TAGS
import hashlib
import time

def download_image(url, filename, chunk_size=8192):
    """
    下载图像文件
    
    参数:
        url: 图像URL
        filename: 保存的文件名
        chunk_size: 分块大小
    
    返回:
        bool: 下载是否成功
    """
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(filename, 'wb') as file:
            for chunk in response.iter_content(chunk_size=chunk_size):
                if chunk:
                    file.write(chunk)
                    downloaded += len(chunk)
                    
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"下载进度: {progress:.1f}%", end='\r')
        
        print(f"\n图像下载完成: {filename}")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"下载失败: {e}")
        return False
    except IOError as e:
        print(f"文件保存失败: {e}")
        return False

def get_image_info(filename):
    """
    获取图像基本信息
    
    参数:
        filename: 图像文件名
    
    返回:
        dict: 图像信息字典
    """
    try:
        with Image.open(filename) as img:
            info = {
                'filename': filename,
                'format': img.format,
                'size': img.size,
                'mode': img.mode,
                'file_size': os.path.getsize(filename)
            }
            
            print(f"图像信息:")
            print(f"  文件名: {info['filename']}")
            print(f"  格式: {info['format']}")
            print(f"  尺寸: {info['size'][0]} x {info['size'][1]} 像素")
            print(f"  模式: {info['mode']}")
            print(f"  文件大小: {info['file_size']} 字节")
            
            return info
            
    except Exception as e:
        print(f"读取图像信息失败: {e}")
        return None

def read_exif_data(filename):
    """
    读取图像EXIF元数据
    
    参数:
        filename: 图像文件名
    
    返回:
        dict: EXIF数据字典
    """
    try:
        with Image.open(filename) as img:
            exif_data = img._getexif()
            
            if exif_data is not None:
                exif_dict = {}
                print(f"\nEXIF元数据:")
                
                for tag_id, value in exif_data.items():
                    tag = TAGS.get(tag_id, tag_id)
                    exif_dict[tag] = value
                    print(f"  {tag}: {value}")
                
                return exif_dict
            else:
                print("该图像没有EXIF数据")
                return {}
                
    except Exception as e:
        print(f"读取EXIF数据失败: {e}")
        return None

def verify_image_integrity(filename):
    """
    验证图像文件完整性
    
    参数:
        filename: 图像文件名
    
    返回:
        bool: 文件是否完整
    """
    try:
        with Image.open(filename) as img:
            img.verify()
        
        print(f"图像文件完整性验证通过: {filename}")
        return True
        
    except Exception as e:
        print(f"图像文件损坏: {e}")
        return False

def calculate_file_hash(filename):
    """
    计算文件MD5哈希值
    
    参数:
        filename: 文件名
    
    返回:
        str: MD5哈希值
    """
    try:
        hash_md5 = hashlib.md5()
        with open(filename, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        
        hash_value = hash_md5.hexdigest()
        print(f"文件MD5哈希: {hash_value}")
        return hash_value
        
    except Exception as e:
        print(f"计算哈希值失败: {e}")
        return None

def batch_download_images(urls, download_dir="downloads"):
    """
    批量下载图像
    
    参数:
        urls: URL列表
        download_dir: 下载目录
    
    返回:
        list: 成功下载的文件列表
    """
    if not os.path.exists(download_dir):
        os.makedirs(download_dir)
    
    downloaded_files = []
    
    for i, url in enumerate(urls, 1):
        filename = os.path.join(download_dir, f"image_{i}.jpg")
        print(f"\n下载第 {i} 张图像...")
        
        if download_image(url, filename):
            downloaded_files.append(filename)
            
            if get_image_info(filename):
                verify_image_integrity(filename)
                calculate_file_hash(filename)
        
        time.sleep(0.5)
    
    return downloaded_files

def main():
    """主函数"""
    print("Python图像处理程序")
    print("=" * 30)
    
    urls = [
        "https://picsum.photos/400/300",
        "https://picsum.photos/600/400", 
        "https://picsum.photos/800/600"
    ]
    
    downloaded_files = batch_download_images(urls)
    
    print(f"\n下载完成！共下载 {len(downloaded_files)} 张图像")
    
    if downloaded_files:
        print("\n分析第一张图像的详细信息:")
        first_image = downloaded_files[0]
        get_image_info(first_image)
        read_exif_data(first_image)

if __name__ == "__main__":
    main()
