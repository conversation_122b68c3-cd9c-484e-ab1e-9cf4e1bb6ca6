import requests
import os
from PIL import Image
from PIL.ExifTags import TAGS

def download_image(url, filename):
    """
    下载图像文件
    请在空白处填入正确的代码
    """
    try:
        # 发送GET请求，使用stream=True进行流式下载
        response = requests.get(url, stream=_______)
        response.raise_for_status()
        
        # 获取文件总大小
        total_size = int(response.headers.get('_______', 0))
        downloaded = 0
        
        # 以二进制写入模式打开文件
        with open(filename, '_____') as file:
            # 分块读取响应内容
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    # 写入文件
                    file._______(chunk)
                    downloaded += len(chunk)
                    
                    # 显示下载进度
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"下载进度: {progress:.1f}%", end='\r')
        
        print(f"\n图像下载完成: {filename}")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"下载失败: {e}")
        return False

def get_image_info(filename):
    """
    获取图像基本信息
    请在空白处填入正确的代码
    """
    try:
        # 使用PIL打开图像
        with Image._______(filename) as img:
            print(f"图像信息:")
            print(f"  文件名: {filename}")
            # 获取图像格式
            print(f"  格式: {img._______}")
            # 获取图像尺寸
            print(f"  尺寸: {img._______[0]} x {img._______[1]} 像素")
            # 获取图像模式
            print(f"  模式: {img._______}")
            # 获取文件大小
            print(f"  文件大小: {os.path.getsize(filename)} 字节")
            
            return True
            
    except Exception as e:
        print(f"读取图像信息失败: {e}")
        return False

def read_exif_data(filename):
    """
    读取图像EXIF元数据
    请在空白处填入正确的代码
    """
    try:
        with Image.open(filename) as img:
            # 获取EXIF数据
            exif_data = img._______()
            
            if exif_data is not None:
                print(f"\nEXIF元数据:")
                
                # 遍历EXIF数据
                for tag_id, value in exif_data.items():
                    # 获取标签名称
                    tag = TAGS.get(_______, tag_id)
                    print(f"  {tag}: {value}")
                
                return True
            else:
                print("该图像没有EXIF数据")
                return False
                
    except Exception as e:
        print(f"读取EXIF数据失败: {e}")
        return False

def main():
    """
    主函数
    请在空白处填入正确的代码
    """
    print("Python图像处理程序")
    print("=" * 30)
    
    # 创建下载目录
    download_dir = "downloads"
    if not os.path.exists(download_dir):
        os._______(download_dir)
    
    # 图像URL
    url = "https://picsum.photos/400/300"
    filename = os.path.join(download_dir, "test_image.jpg")
    
    print(f"开始下载图像: {url}")
    
    # 下载图像
    if download_image(url, filename):
        print("\n分析图像信息:")
        # 获取图像信息
        get_image_info(filename)
        # 读取EXIF数据
        read_exif_data(filename)
    else:
        print("图像下载失败")

# 运行程序
if __name__ == "__main__":
    main()

# 填空答案提示:
# 1. True
# 2. 'content-length'
# 3. 'wb'
# 4. write
# 5. open
# 6. format
# 7. size, size
# 8. mode
# 9. _getexif
# 10. tag_id
# 11. makedirs
