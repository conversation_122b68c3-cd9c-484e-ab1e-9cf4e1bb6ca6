"""
Python图像处理程序 - 困难难度

任务描述:
创建一个完整的图像下载和处理程序，实现以下功能：

基础功能 (必须实现):
1. 图像下载功能 - 支持分块下载和进度显示
2. 图像信息获取 - 格式、尺寸、模式、文件大小
3. EXIF元数据读取 - 读取并显示图像的元数据
4. 文件完整性验证 - 验证下载的图像是否完整
5. 批量下载功能 - 支持下载多张图像

可选挑战功能 (额外加分):
6. 图像格式转换 - 将图像转换为不同格式
7. 图像缩放功能 - 生成不同尺寸的缩略图
8. 文件哈希计算 - 计算MD5或SHA256哈希值
9. 下载重试机制 - 失败时自动重试
10. 配置文件支持 - 从配置文件读取下载参数

API信息:
- 随机图像API: https://picsum.photos/{width}/{height}
- 示例: https://picsum.photos/400/300

所需库:
- requests: HTTP请求
- PIL (Pillow): 图像处理
- os: 文件操作
- hashlib: 哈希计算 (可选)
- json: 配置文件 (可选)

程序结构建议:
1. 导入所需库
2. 定义下载函数
3. 定义图像分析函数
4. 定义可选功能函数
5. 主函数协调所有功能

评分标准:
- 基础功能完整性 (70分)
- 代码质量和异常处理 (15分)
- 可选功能实现 (15分)

开始编写你的代码:
"""

# 在这里编写你的完整程序

# 导入库


# 定义函数


# 主函数


# 运行程序
if __name__ == "__main__":
    pass
