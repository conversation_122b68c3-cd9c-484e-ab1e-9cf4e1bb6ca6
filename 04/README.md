# Python图像处理入门教学材料

基于 Notebook/L4.ipynb 的 Python 图像处理教学材料，适合小学生学习。

## 教学内容

本课程教授图像API使用和文件处理：

1. **二进制数据概念** - 理解文本与图像数据的区别
2. **图像API调用** - 使用picsum.photos等API获取图像
3. **分块下载技术** - stream=True和response.iter_content()
4. **文件操作** - 'wb'模式写入二进制文件
5. **异常处理** - 网络请求的错误处理
6. **图像元数据** - EXIF信息读取和分析

## 文件夹结构

### teacher_demo/
老师课堂演示用的完整代码
- `image_downloader_demo.py` - 完整的图像下载器演示程序
- `image_server.py` - 本地图像服务器（辅助工具）

### answers/
标准答案文件
- `image_processor_complete.py` - 完整的参考答案

### templates/
学生练习模板（三种难度）
- `image_processor_easy.py` - 简单难度（填空题）
- `image_processor_medium.py` - 中等难度（提供骨架）
- `image_processor_hard.py` - 困难难度（仅框架）

### 辅助工具
- `auto_grader.py` - 自动测试打分工具
- `api_test_helper.py` - API连接测试工具

## 难度说明

### 简单难度（image_processor_easy.py）
- 几乎完整的程序代码
- 在关键位置设置填空
- 适合初学者理解图像下载流程

### 中等难度（image_processor_medium.py）
- 提供函数框架和复杂逻辑
- 需要学生补充简单的实现代码
- 适合有一定基础的学生

### 困难难度（image_processor_hard.py）
- 仅提供任务描述和API信息
- 需要学生独立完成所有代码
- 包含可选的挑战功能
- 适合有经验的学生

## 使用说明

### 学生使用
1. 根据自己水平选择对应难度的模板文件
2. 完成代码编写
3. 运行程序查看结果
4. 使用自动打分工具检验成果

### 老师使用
1. 使用 `teacher_demo/` 中的代码进行课堂演示
2. 参考 `answers/` 中的标准答案
3. 使用 `auto_grader.py` 对学生作业进行自动评分

### 测试工具
运行 `api_test_helper.py` 可以：
- 测试图像API的连接状态
- 验证下载功能
- 检查文件完整性

## 评分标准

自动打分工具评分项目：
- 导入库 (5分)
- 图像下载函数 (25分)
- 分块下载实现 (20分)
- 文件保存功能 (15分)
- 异常处理 (15分)
- EXIF读取功能 (10分)
- 代码质量和奖励功能 (10分)
