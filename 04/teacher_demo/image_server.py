import http.server
import socketserver
import os
import random
from PIL import Image, ImageDraw, ImageFont
import io

class ImageHandler(http.server.SimpleHTTPRequestHandler):
    """自定义图像服务器处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        path = self.path
        
        if path.startswith('/image/'):
            self.serve_random_image(path)
        elif path.startswith('/size/'):
            self.serve_sized_image(path)
        else:
            self.send_error(404, "Not Found")
    
    def serve_random_image(self, path):
        """提供随机图像"""
        try:
            width = random.randint(200, 800)
            height = random.randint(200, 600)
            
            image = self.create_test_image(width, height)
            
            img_buffer = io.BytesIO()
            image.save(img_buffer, format='JPEG')
            img_data = img_buffer.getvalue()
            
            self.send_response(200)
            self.send_header('Content-Type', 'image/jpeg')
            self.send_header('Content-Length', str(len(img_data)))
            self.end_headers()
            
            self.wfile.write(img_data)
            
        except Exception as e:
            self.send_error(500, f"Server Error: {e}")
    
    def serve_sized_image(self, path):
        """提供指定尺寸的图像"""
        try:
            parts = path.split('/')
            if len(parts) >= 4:
                width = int(parts[2])
                height = int(parts[3])
            else:
                width, height = 400, 300
            
            image = self.create_test_image(width, height)
            
            img_buffer = io.BytesIO()
            image.save(img_buffer, format='JPEG')
            img_data = img_buffer.getvalue()
            
            self.send_response(200)
            self.send_header('Content-Type', 'image/jpeg')
            self.send_header('Content-Length', str(len(img_data)))
            self.end_headers()
            
            self.wfile.write(img_data)
            
        except Exception as e:
            self.send_error(500, f"Server Error: {e}")
    
    def create_test_image(self, width, height):
        """创建测试图像"""
        colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange']
        color = random.choice(colors)
        
        image = Image.new('RGB', (width, height), color)
        draw = ImageDraw.Draw(image)
        
        try:
            font = ImageFont.load_default()
        except:
            font = None
        
        text = f"{width}x{height}"
        if font:
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
        else:
            text_width = len(text) * 6
            text_height = 11
        
        x = (width - text_width) // 2
        y = (height - text_height) // 2
        
        draw.text((x, y), text, fill='white', font=font)
        
        for _ in range(5):
            x1 = random.randint(0, width)
            y1 = random.randint(0, height)
            x2 = random.randint(0, width)
            y2 = random.randint(0, height)
            draw.line([(x1, y1), (x2, y2)], fill='white', width=2)
        
        return image

def start_server(port=8000):
    """启动图像服务器"""
    try:
        with socketserver.TCPServer(("", port), ImageHandler) as httpd:
            print(f"图像服务器启动在端口 {port}")
            print(f"访问示例:")
            print(f"  随机图像: http://localhost:{port}/image/random")
            print(f"  指定尺寸: http://localhost:{port}/size/400/300")
            print("按 Ctrl+C 停止服务器")
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"服务器启动失败: {e}")

if __name__ == "__main__":
    start_server()
