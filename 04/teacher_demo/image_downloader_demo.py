import requests
import os
from PIL import Image
from PIL.ExifTags import TAGS
import time

def download_image_simple(url, filename):
    """简单图像下载演示"""
    print(f"开始下载图像: {url}")
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        
        with open(filename, 'wb') as file:
            file.write(response.content)
        
        print(f"图像已保存为: {filename}")
        print(f"文件大小: {len(response.content)} 字节")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"下载失败: {e}")
        return False

def download_image_chunked(url, filename, chunk_size=8192):
    """分块下载演示"""
    print(f"开始分块下载图像: {url}")
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(filename, 'wb') as file:
            for chunk in response.iter_content(chunk_size=chunk_size):
                if chunk:
                    file.write(chunk)
                    downloaded += len(chunk)
                    
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"下载进度: {progress:.1f}%", end='\r')
        
        print(f"\n图像已保存为: {filename}")
        print(f"总下载大小: {downloaded} 字节")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"下载失败: {e}")
        return False

def get_image_info(filename):
    """获取图像基本信息"""
    try:
        with Image.open(filename) as img:
            print(f"\n图像信息:")
            print(f"文件名: {filename}")
            print(f"格式: {img.format}")
            print(f"尺寸: {img.size[0]} x {img.size[1]} 像素")
            print(f"模式: {img.mode}")
            
            return {
                'format': img.format,
                'size': img.size,
                'mode': img.mode
            }
    except Exception as e:
        print(f"读取图像信息失败: {e}")
        return None

def get_exif_data(filename):
    """读取EXIF元数据"""
    try:
        with Image.open(filename) as img:
            exif_data = img._getexif()
            
            if exif_data is not None:
                print(f"\nEXIF元数据:")
                for tag_id, value in exif_data.items():
                    tag = TAGS.get(tag_id, tag_id)
                    print(f"{tag}: {value}")
                return exif_data
            else:
                print("该图像没有EXIF数据")
                return None
                
    except Exception as e:
        print(f"读取EXIF数据失败: {e}")
        return None

def demonstrate_binary_concept():
    """演示二进制概念"""
    print("=== 二进制数据概念演示 ===")
    
    text_data = "Hello World"
    print(f"文本数据: {text_data}")
    print(f"文本的字节表示: {text_data.encode('utf-8')}")
    
    with open("temp_text.txt", 'w') as f:
        f.write(text_data)
    
    with open("temp_text.txt", 'rb') as f:
        binary_data = f.read()
        print(f"文件的二进制数据: {binary_data}")
    
    os.remove("temp_text.txt")
    print()

def main():
    """主演示函数"""
    print("Python图像处理入门 - 老师演示")
    print("=" * 40)
    
    demonstrate_binary_concept()
    
    if not os.path.exists('downloads'):
        os.makedirs('downloads')
    
    # 演示1: 简单下载
    print("=== 演示1: 简单图像下载 ===")
    url1 = "https://picsum.photos/300/200"
    filename1 = "downloads/demo_simple.jpg"
    
    if download_image_simple(url1, filename1):
        get_image_info(filename1)
    
    time.sleep(1)
    
    # 演示2: 分块下载
    print("\n=== 演示2: 分块下载 ===")
    url2 = "https://picsum.photos/800/600"
    filename2 = "downloads/demo_chunked.jpg"
    
    if download_image_chunked(url2, filename2):
        get_image_info(filename2)
        get_exif_data(filename2)
    
    # 演示3: 不同尺寸图像
    print("\n=== 演示3: 下载不同尺寸图像 ===")
    sizes = [(200, 200), (400, 300), (600, 400)]
    
    for i, (width, height) in enumerate(sizes, 1):
        url = f"https://picsum.photos/{width}/{height}"
        filename = f"downloads/demo_size_{i}.jpg"
        print(f"\n下载 {width}x{height} 图像...")
        
        if download_image_simple(url, filename):
            get_image_info(filename)
    
    print("\n演示完成！请查看 downloads 文件夹中的图像文件。")

if __name__ == "__main__":
    main()
