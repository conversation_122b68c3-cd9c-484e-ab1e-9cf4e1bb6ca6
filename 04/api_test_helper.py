import requests
import time
import os
from PIL import Image

def test_api_connection(url, timeout=10):
    """测试API连接"""
    try:
        print(f"测试连接: {url}")
        response = requests.get(url, timeout=timeout)
        
        if response.status_code == 200:
            print(f"✓ 连接成功 (状态码: {response.status_code})")
            print(f"  响应大小: {len(response.content)} 字节")
            
            content_type = response.headers.get('content-type', 'unknown')
            print(f"  内容类型: {content_type}")
            
            return True
        else:
            print(f"✗ 连接失败 (状态码: {response.status_code})")
            return False
            
    except requests.exceptions.Timeout:
        print(f"✗ 连接超时 (超过 {timeout} 秒)")
        return False
    except requests.exceptions.ConnectionError:
        print("✗ 连接错误 - 请检查网络连接")
        return False
    except Exception as e:
        print(f"✗ 未知错误: {e}")
        return False

def test_image_download(url, filename="test_image.jpg"):
    """测试图像下载功能"""
    try:
        print(f"\n测试下载: {url}")
        
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(filename, 'wb') as file:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    file.write(chunk)
        
        print(f"✓ 下载成功: {filename}")
        
        # 验证文件
        if os.path.exists(filename):
            file_size = os.path.getsize(filename)
            print(f"  文件大小: {file_size} 字节")
            
            # 验证是否为有效图像
            try:
                with Image.open(filename) as img:
                    print(f"  图像格式: {img.format}")
                    print(f"  图像尺寸: {img.size[0]} x {img.size[1]}")
                    print("✓ 图像文件有效")
                    
                # 清理测试文件
                os.remove(filename)
                print(f"  已清理测试文件: {filename}")
                
                return True
                
            except Exception as e:
                print(f"✗ 图像文件无效: {e}")
                if os.path.exists(filename):
                    os.remove(filename)
                return False
        else:
            print("✗ 文件未创建")
            return False
            
    except Exception as e:
        print(f"✗ 下载失败: {e}")
        if os.path.exists(filename):
            os.remove(filename)
        return False

def test_different_sizes():
    """测试不同尺寸的图像"""
    print("\n测试不同尺寸图像:")
    sizes = [
        (200, 200),
        (400, 300),
        (800, 600),
        (1024, 768)
    ]
    
    success_count = 0
    
    for width, height in sizes:
        url = f"https://picsum.photos/{width}/{height}"
        filename = f"test_{width}x{height}.jpg"
        
        print(f"\n  测试 {width}x{height} 图像...")
        if test_image_download(url, filename):
            success_count += 1
        
        time.sleep(0.5)  # 避免请求过快
    
    print(f"\n尺寸测试结果: {success_count}/{len(sizes)} 成功")
    return success_count == len(sizes)

def test_stream_download():
    """测试流式下载"""
    print("\n测试流式下载:")
    url = "https://picsum.photos/600/400"
    filename = "test_stream.jpg"
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        print(f"  开始流式下载，总大小: {total_size} 字节")
        
        with open(filename, 'wb') as file:
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    file.write(chunk)
                    downloaded += len(chunk)
                    
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"  下载进度: {progress:.1f}%", end='\r')
        
        print(f"\n✓ 流式下载成功")
        
        # 清理文件
        if os.path.exists(filename):
            os.remove(filename)
        
        return True
        
    except Exception as e:
        print(f"✗ 流式下载失败: {e}")
        if os.path.exists(filename):
            os.remove(filename)
        return False

def run_all_tests():
    """运行所有测试"""
    print("图像API测试工具")
    print("=" * 40)
    
    tests = [
        ("基础连接测试", lambda: test_api_connection("https://picsum.photos/200/200")),
        ("图像下载测试", lambda: test_image_download("https://picsum.photos/300/200")),
        ("不同尺寸测试", test_different_sizes),
        ("流式下载测试", test_stream_download)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 20)
        
        try:
            if test_func():
                print(f"✓ {test_name} 通过")
                passed += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
        
        time.sleep(1)
    
    print(f"\n测试总结:")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("✓ 所有测试通过，API工作正常！")
    else:
        print("✗ 部分测试失败，请检查网络连接或API状态")

if __name__ == "__main__":
    run_all_tests()
