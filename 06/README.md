# 网络模拟器教学工具

## 项目简介

这是一个专为小学生设计的网络模拟教学工具，通过Web界面提供直观的网络配置学习环境。

## 文件说明

### 核心文件
- `network_simulator.py` - 主服务器程序，提供Web服务和API接口
- `auto_grader.py` - 自动测试打分系统，评估学生网络配置

### 模板文件
- `templates/index.html` - 登录页面
- `templates/simulator.html` - 学生操作界面
- `templates/teacher.html` - 教师管理界面

### 静态资源
- `static/css/style.css` - 样式文件
- `static/js/simulator.js` - 前端交互逻辑

### 配置文件
- `requirements.txt` - Python依赖包列表
- `start_server.py` - 便捷启动脚本

## 功能特性

### 学生端功能
- 多用户独立环境
- 拖拽式设备添加（路由器、客户端）
- **直观的拖拽连接功能**（拖拽设备端口创建连接）
- **智能网线渲染**（SVG网线随设备移动自动更新，点击断开）
- **动态端口系统**（客户端1个端口，路由器4个端口，可动态添加）
- **精确端口定位**（端口正确显示在设备边缘）
- **智能界面交互**（选中设备自动更新右侧面板）
- **接口选择器**（下拉选择接口，自动显示当前配置）
- **DHCP支持**（一键开关，自动分配IP地址）
- 动态接口创建和配置
- IP地址和接口配置
- **增强的ping命令**（4个结果，本地地址低延迟，远程地址高延迟）
- **智能延迟模拟**（本地0.1-2.0ms，远程1.0-100.0ms）
- 网络命令执行（ip a, ip route, ping, ipconfig, ifconfig）
- 实时网络拓扑显示
- **优化的模板系统**（连接线正确从端口出发）
- 练习场景引导学习
- 实时网络配置验证

### 🆕 新增教学功能
- **分步骤教学课程**（基于L6.ipynb内容设计）
  - 第1课：认识IP地址（私有IP、公网IP、本地回环地址）
  - 第2课：搭建家庭网络（路由器配置、设备连接）
  - 第3课：DHCP自动分配IP（地址池配置、动态分配）
  - 第4课：NAT和端口映射（网络地址转换、服务发布）
- **IP地址学习模块**
  - IP地址分类器（自动识别IP类型）
  - 交互式练习题（巩固理论知识）
  - 延迟模拟（体验不同网络距离）
- **网络诊断工具**
  - 类似ipconfig/ifconfig的配置查看
  - 增强的ping命令（包含教育性解释）
  - 网络连通性测试报告
  - 设备详细信息查看
- **路由器高级功能**
  - DHCP服务器配置（地址池、客户端列表）
  - NAT网络地址转换
  - 端口映射规则配置
  - 路由器状态监控

### 教师端功能
- 学生作业实时监控
- 自动评分系统
- 分数统计和分析
- CSV格式成绩导出
- 个性化改进建议

### 评分标准
- 设备数量（20分）
- 网络连通性（30分）
- IP配置正确性（25分）
- 路由复杂度（15分）
- 网络设计合理性（10分）

## 安装运行

### 方法一：使用启动脚本（推荐）
```bash
python start_server.py
```

### 方法二：手动启动
1. 安装依赖
```bash
pip install -r requirements.txt
```

2. 启动服务
```bash
python network_simulator.py
```

3. 访问地址
- 学生端：http://localhost:8080
- 教师端：http://localhost:8080/teacher

## 使用说明

### 学生操作流程
1. 输入用户名登录
2. 添加网络设备（路由器、客户端）
3. 使用连接模式连接设备
4. 配置设备接口和IP地址
5. 执行网络命令测试连通性
6. 使用验证功能检查配置
7. 尝试练习场景提升技能

### 教师操作流程
1. 访问教师端页面
2. 查看学生实时进度
3. 查看自动评分结果
4. 导出成绩数据
5. 提供个性化指导

## 支持的网络命令

### 基础命令
- `ip a` / `ip addr` - 显示网络接口信息（Linux风格）
- `ip route` - 显示路由表
- `ipconfig` - 显示网络配置（Windows风格）
- `ipconfig /all` - 显示详细网络配置
- `ifconfig` - 显示网络接口信息（Unix风格）
- `ping <目标IP>` - 测试网络连通性（增强版，包含教育性解释）

### 🆕 新增功能
- **智能命令解释**：每个命令都包含详细的教育性说明
- **延迟模拟**：根据IP地址类型模拟真实的网络延迟
- **多平台支持**：同时支持Windows、Linux、Mac的命令格式

## 技术架构

- 后端：Python Flask框架
- 前端：HTML5 + CSS3 + JavaScript
- 数据存储：内存存储（会话级别）
- 网络模拟：虚拟设备对象模型
