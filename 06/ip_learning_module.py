import ipaddress
import random

class IPAddressLearningModule:
    def __init__(self):
        self.private_ranges = [
            ('10.0.0.0', '**************'),
            ('**********', '**************'),
            ('***********', '***************')
        ]
        self.loopback_range = ('*********', '***************')
        
    def classify_ip_address(self, ip_str):
        """分类IP地址类型"""
        try:
            ip = ipaddress.IPv4Address(ip_str)
            
            # 检查是否是本地回环地址
            if self._is_loopback(ip_str):
                return {
                    'type': 'loopback',
                    'name': '本地回环地址',
                    'description': '指向设备自身，用于本地通信和测试',
                    'example': '127.0.0.1',
                    'characteristics': [
                        '不经过网络接口',
                        '延迟极低（通常<1ms）',
                        '用于本地服务测试',
                        '永远可达（除非系统故障）'
                    ]
                }
            
            # 检查是否是私有IP地址
            elif self._is_private(ip_str):
                return {
                    'type': 'private',
                    'name': '私有IP地址',
                    'description': '仅在局域网内有效的地址，就像家里的房间号',
                    'example': '*************',
                    'characteristics': [
                        '只能在局域网内使用',
                        '需要NAT转换才能访问互联网',
                        '可以重复使用（不同网络中）',
                        '节省公网IP地址资源'
                    ]
                }
            
            # 其他情况视为公网IP
            else:
                return {
                    'type': 'public',
                    'name': '公网IP地址',
                    'description': '在整个互联网上唯一的地址，就像家庭住址',
                    'example': '*******',
                    'characteristics': [
                        '全球唯一，不能重复',
                        '可以直接在互联网上路由',
                        '由ISP或云服务商分配',
                        '通常需要付费获得'
                    ]
                }
                
        except ipaddress.AddressValueError:
            return {
                'type': 'invalid',
                'name': '无效IP地址',
                'description': '输入的不是有效的IPv4地址格式',
                'example': '正确格式：***********',
                'characteristics': [
                    'IP地址由4个数字组成',
                    '每个数字范围是0-255',
                    '数字之间用点号分隔',
                    '不能有前导零（如***********）'
                ]
            }
    
    def _is_private(self, ip_str):
        """检查是否是私有IP地址"""
        try:
            ip = ipaddress.IPv4Address(ip_str)
            for start, end in self.private_ranges:
                if ipaddress.IPv4Address(start) <= ip <= ipaddress.IPv4Address(end):
                    return True
            return False
        except:
            return False
    
    def _is_loopback(self, ip_str):
        """检查是否是本地回环地址"""
        try:
            ip = ipaddress.IPv4Address(ip_str)
            start, end = self.loopback_range
            return ipaddress.IPv4Address(start) <= ip <= ipaddress.IPv4Address(end)
        except:
            return False
    
    def generate_quiz_questions(self, count=5):
        """生成IP地址分类练习题"""
        questions = []
        
        # 预定义的IP地址示例
        ip_examples = [
            ('***********', 'private'),
            ('********', 'private'),
            ('**********', 'private'),
            ('127.0.0.1', 'loopback'),
            ('*********', 'loopback'),
            ('*******', 'public'),
            ('***************', 'public'),
            ('***********', 'public'),
            ('*************', 'private'),
            ('***********', 'private')
        ]
        
        # 随机选择题目
        selected = random.sample(ip_examples, min(count, len(ip_examples)))
        
        for ip, correct_type in selected:
            classification = self.classify_ip_address(ip)
            
            # 生成选项
            options = [
                {'value': 'private', 'text': '私有IP地址'},
                {'value': 'public', 'text': '公网IP地址'},
                {'value': 'loopback', 'text': '本地回环地址'}
            ]
            
            questions.append({
                'ip_address': ip,
                'question': f'请判断IP地址 {ip} 的类型：',
                'options': options,
                'correct_answer': correct_type,
                'explanation': classification['description'],
                'characteristics': classification['characteristics']
            })
        
        return questions
    
    def get_learning_content(self):
        """获取IP地址学习内容"""
        return {
            'title': 'IP地址类型学习',
            'sections': [
                {
                    'title': '什么是IP地址？',
                    'content': [
                        'IP地址就像网络世界中的"地址"，每台连接到网络的设备都需要一个唯一的IP地址。',
                        '就像寄快递需要写清楚收件人地址一样，网络通信也需要明确的目标地址。',
                        'IP地址由4个数字组成，每个数字的范围是0-255，用点号分隔。'
                    ]
                },
                {
                    'title': '私有IP地址（房间号）',
                    'content': [
                        '私有IP地址就像你家里的"房间号"（主卧、次卧、书房）。',
                        '只在你自己的局域网（家庭网络）内有效。',
                        '常见范围：192.168.x.x、10.x.x.x、172.16.x.x到172.31.x.x',
                        '例子：你跟妈妈说"我在书房"，她能听懂，但快递员听不懂。'
                    ]
                },
                {
                    'title': '公网IP地址（家庭住址）',
                    'content': [
                        '公网IP地址就像你家的"完整家庭住址"。',
                        '在整个互联网上是唯一的，是你的网络在世界上的身份标识。',
                        '例子：***************（这是一个著名的DNS服务器地址）',
                        '就像快递员需要完整地址才能找到你家一样。'
                    ]
                },
                {
                    'title': '本地回环地址（自言自语）',
                    'content': [
                        '本地回环地址是一个特殊的地址：127.0.0.1',
                        '它指向设备自身，就像你对自己说话一样。',
                        '程序员用它来测试自己电脑上的服务是否正常运行。',
                        '数据不会真的发送到网络上，而是在设备内部"转一圈"就回来了。'
                    ]
                }
            ],
            'interactive_examples': [
                {
                    'scenario': '家庭网络场景',
                    'description': '想象你家的网络设置',
                    'devices': [
                        {'name': '路由器', 'ip': '***********', 'type': 'private', 'role': '网关（大门）'},
                        {'name': '你的电脑', 'ip': '*************', 'type': 'private', 'role': '房间号'},
                        {'name': '妈妈的手机', 'ip': '*************', 'type': 'private', 'role': '房间号'},
                        {'name': '本地测试', 'ip': '127.0.0.1', 'type': 'loopback', 'role': '自己跟自己说话'}
                    ]
                }
            ]
        }
    
    def simulate_ping_delay(self, source_ip, target_ip):
        """模拟不同类型IP地址的ping延迟"""
        source_type = self.classify_ip_address(source_ip)['type']
        target_type = self.classify_ip_address(target_ip)['type']
        
        # 本地回环地址延迟最低
        if target_type == 'loopback':
            return round(random.uniform(0.01, 0.1), 2)
        
        # 同一局域网内的私有IP地址
        elif source_type == 'private' and target_type == 'private':
            # 检查是否在同一网段
            if self._same_subnet(source_ip, target_ip):
                return round(random.uniform(0.1, 2.0), 2)
            else:
                return round(random.uniform(1.0, 5.0), 2)
        
        # 访问公网IP地址
        elif target_type == 'public':
            return round(random.uniform(10.0, 100.0), 2)
        
        # 其他情况
        else:
            return round(random.uniform(1.0, 10.0), 2)
    
    def _same_subnet(self, ip1, ip2):
        """简单检查两个IP是否在同一子网（前三段相同）"""
        try:
            parts1 = ip1.split('.')[:3]
            parts2 = ip2.split('.')[:3]
            return parts1 == parts2
        except:
            return False
