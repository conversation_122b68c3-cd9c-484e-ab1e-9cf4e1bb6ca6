class TeachingGuideSystem:
    def __init__(self):
        self.lessons = {
            'lesson1_ip_basics': {
                'title': '第1课：认识IP地址',
                'description': '学习什么是IP地址，就像网络世界的门牌号',
                'duration': '15分钟',
                'difficulty': '简单',
                'steps': [
                    {
                        'step': 1,
                        'title': '什么是IP地址？',
                        'content': 'IP地址就像你家的门牌号，每台连接到网络的设备都需要一个唯一的地址。',
                        'action': '点击"IP地址学习"按钮，尝试识别不同类型的IP地址',
                        'example': '*********** 是一个私有IP地址，就像你家里的房间号',
                        'quiz': {
                            'question': '127.0.0.1 是什么类型的IP地址？',
                            'options': ['私有IP', '公网IP', '本地回环地址'],
                            'correct': 2,
                            'explanation': '127.0.0.1是本地回环地址，指向设备自身'
                        }
                    },
                    {
                        'step': 2,
                        'title': '私有IP vs 公网IP',
                        'content': '私有IP像房间号，只在家里有效；公网IP像家庭住址，全世界唯一。',
                        'action': '添加一个客户端，为它配置私有IP地址 ***********00',
                        'example': '你的手机连WiFi时获得的是私有IP，而你家的路由器有公网IP',
                        'quiz': {
                            'question': '***********00 是什么类型的IP地址？',
                            'options': ['私有IP', '公网IP', '本地回环地址'],
                            'correct': 0,
                            'explanation': '192.168.x.x 是私有IP地址范围'
                        }
                    },
                    {
                        'step': 3,
                        'title': '测试不同地址的延迟',
                        'content': '不同类型的IP地址有不同的网络延迟特征。',
                        'action': '使用ping命令测试 127.0.0.1 和 *******，观察延迟差异',
                        'example': '本地地址延迟很低（<1ms），远程地址延迟较高（>10ms）',
                        'quiz': {
                            'question': '为什么ping 127.0.0.1的延迟比ping *******低？',
                            'options': ['网速更快', '距离更近', '不经过网络接口'],
                            'correct': 2,
                            'explanation': '127.0.0.1是本地回环，数据不经过网络接口'
                        }
                    }
                ]
            },
            'lesson2_home_network': {
                'title': '第2课：搭建家庭网络',
                'description': '学习如何搭建一个简单的家庭网络',
                'duration': '20分钟',
                'difficulty': '中等',
                'steps': [
                    {
                        'step': 1,
                        'title': '添加路由器',
                        'content': '路由器是家庭网络的中心，就像一个聪明的交通警察。',
                        'action': '添加一个路由器，命名为"家用路由器"',
                        'example': '路由器连接所有家庭设备，并提供上网功能',
                        'quiz': {
                            'question': '路由器在家庭网络中的主要作用是什么？',
                            'options': ['存储文件', '连接设备并提供网关', '播放音乐'],
                            'correct': 1,
                            'explanation': '路由器连接所有设备并作为上网的网关'
                        }
                    },
                    {
                        'step': 2,
                        'title': '配置路由器IP',
                        'content': '路由器需要一个IP地址作为网关地址。',
                        'action': '为路由器的eth0接口配置IP：***********/24',
                        'example': '*********** 是常见的家用路由器地址',
                        'quiz': {
                            'question': '为什么路由器通常使用***********？',
                            'options': ['随机选择', '这是私有IP范围的常用地址', '政府规定'],
                            'correct': 1,
                            'explanation': '***********是私有IP范围内的标准网关地址'
                        }
                    },
                    {
                        'step': 3,
                        'title': '添加家庭设备',
                        'content': '现在添加家里的各种设备。',
                        'action': '添加3个客户端：电脑、手机、平板',
                        'example': '现代家庭通常有多个联网设备',
                        'quiz': {
                            'question': '一个家庭网络通常包含哪些设备？',
                            'options': ['只有电脑', '电脑、手机、平板等', '只有路由器'],
                            'correct': 1,
                            'explanation': '现代家庭有各种联网设备'
                        }
                    },
                    {
                        'step': 4,
                        'title': '连接设备到路由器',
                        'content': '所有设备都需要连接到路由器才能上网。',
                        'action': '将所有客户端连接到路由器的不同接口',
                        'example': '就像所有房间的电话线都连到总机一样',
                        'quiz': {
                            'question': '为什么所有设备都要连接到路由器？',
                            'options': ['为了好看', '路由器是上网的唯一出口', '节省电费'],
                            'correct': 1,
                            'explanation': '路由器是家庭网络连接互联网的网关'
                        }
                    },
                    {
                        'step': 5,
                        'title': '配置设备IP地址',
                        'content': '每个设备都需要一个唯一的IP地址。',
                        'action': '为设备配置IP：电脑***********00，手机***********01，平板***********02',
                        'example': '就像给每个房间分配房间号一样',
                        'quiz': {
                            'question': '为什么每个设备需要不同的IP地址？',
                            'options': ['为了区分设备', '为了安全', '为了速度'],
                            'correct': 0,
                            'explanation': 'IP地址是设备在网络中的唯一标识'
                        }
                    },
                    {
                        'step': 6,
                        'title': '测试网络连通性',
                        'content': '验证所有设备是否能够互相通信。',
                        'action': '使用ping命令测试设备间的连通性',
                        'example': '从电脑ping手机的IP地址',
                        'quiz': {
                            'question': 'ping命令的作用是什么？',
                            'options': ['删除文件', '测试网络连通性', '播放声音'],
                            'correct': 1,
                            'explanation': 'ping用于测试网络设备间是否能够通信'
                        }
                    }
                ]
            },
            'lesson3_dhcp': {
                'title': '第3课：DHCP自动分配IP',
                'description': '学习路由器如何自动给设备分配IP地址',
                'duration': '15分钟',
                'difficulty': '中等',
                'steps': [
                    {
                        'step': 1,
                        'title': '什么是DHCP？',
                        'content': 'DHCP就像宿管阿姨，自动给新来的同学分配宿舍房间号。',
                        'action': '在路由器配置中启用DHCP服务',
                        'example': '你的手机连WiFi时自动获得IP就是DHCP的功劳',
                        'quiz': {
                            'question': 'DHCP的主要作用是什么？',
                            'options': ['自动分配IP地址', '加密数据', '播放音乐'],
                            'correct': 0,
                            'explanation': 'DHCP自动为设备分配IP地址'
                        }
                    },
                    {
                        'step': 2,
                        'title': '配置DHCP地址池',
                        'content': 'DHCP需要一个地址池，就像宿舍的可用房间列表。',
                        'action': '设置DHCP地址池：***********00-*************',
                        'example': '这样DHCP可以分配100个不同的IP地址',
                        'quiz': {
                            'question': '地址池***********00-200可以分配多少个IP？',
                            'options': ['100个', '101个', '200个'],
                            'correct': 1,
                            'explanation': '从100到200包含101个地址'
                        }
                    },
                    {
                        'step': 3,
                        'title': '体验自动分配',
                        'content': '让设备通过DHCP自动获取IP地址。',
                        'action': '为客户端启用DHCP，观察自动分配的IP',
                        'example': '就像新同学报到时自动分配宿舍一样',
                        'quiz': {
                            'question': 'DHCP分配的IP地址会在哪个范围内？',
                            'options': ['任意地址', '地址池范围内', '固定地址'],
                            'correct': 1,
                            'explanation': 'DHCP只能分配地址池范围内的地址'
                        }
                    }
                ]
            },
            'lesson4_nat_port_mapping': {
                'title': '第4课：NAT和端口映射',
                'description': '学习如何让外网访问内网服务',
                'duration': '25分钟',
                'difficulty': '困难',
                'steps': [
                    {
                        'step': 1,
                        'title': '什么是NAT？',
                        'content': 'NAT就像大楼的前台，把外面的访客引导到正确的房间。',
                        'action': '观察路由器的NAT配置',
                        'example': '你家多个设备共用一个公网IP上网就是NAT的功劳',
                        'quiz': {
                            'question': 'NAT的主要作用是什么？',
                            'options': ['地址转换', '数据加密', '文件存储'],
                            'correct': 0,
                            'explanation': 'NAT进行网络地址转换'
                        }
                    },
                    {
                        'step': 2,
                        'title': '搭建内网服务器',
                        'content': '在内网设备上"运行"一个Web服务器。',
                        'action': '选择一台客户端作为服务器，记住它的IP地址',
                        'example': '就像在你房间里开一个小商店',
                        'quiz': {
                            'question': '内网服务器的特点是什么？',
                            'options': ['外网可以直接访问', '只能内网访问', '不能访问'],
                            'correct': 1,
                            'explanation': '内网服务器默认只能从内网访问'
                        }
                    },
                    {
                        'step': 3,
                        'title': '配置端口映射',
                        'content': '端口映射就像给前台留言：8888号来访请带到某某房间。',
                        'action': '配置端口映射：外部8888端口映射到内网服务器的8888端口',
                        'example': '这样外网就能通过路由器访问内网服务了',
                        'quiz': {
                            'question': '端口映射的作用是什么？',
                            'options': ['让外网访问内网服务', '加快网速', '节省流量'],
                            'correct': 0,
                            'explanation': '端口映射允许外网访问内网特定服务'
                        }
                    },
                    {
                        'step': 4,
                        'title': '测试外网访问',
                        'content': '模拟从外网访问内网服务器。',
                        'action': '使用公网IP:8888的格式测试访问',
                        'example': '就像外地朋友通过你家地址找到你的房间',
                        'quiz': {
                            'question': '外网访问内网服务需要什么？',
                            'options': ['只要IP地址', '端口映射配置', '特殊软件'],
                            'correct': 1,
                            'explanation': '需要在路由器上配置端口映射规则'
                        }
                    }
                ]
            }
        }
        
        self.user_progress = {}  # 用户学习进度
    
    def get_lesson_list(self):
        """获取课程列表"""
        return {
            lesson_id: {
                'title': lesson['title'],
                'description': lesson['description'],
                'duration': lesson['duration'],
                'difficulty': lesson['difficulty'],
                'step_count': len(lesson['steps'])
            }
            for lesson_id, lesson in self.lessons.items()
        }
    
    def get_lesson_content(self, lesson_id):
        """获取课程详细内容"""
        return self.lessons.get(lesson_id)
    
    def get_user_progress(self, user_id):
        """获取用户学习进度"""
        return self.user_progress.get(user_id, {})
    
    def update_user_progress(self, user_id, lesson_id, step_number, completed=False):
        """更新用户学习进度"""
        if user_id not in self.user_progress:
            self.user_progress[user_id] = {}
        
        if lesson_id not in self.user_progress[user_id]:
            self.user_progress[user_id][lesson_id] = {
                'current_step': 1,
                'completed_steps': [],
                'completed': False,
                'start_time': None,
                'completion_time': None
            }
        
        progress = self.user_progress[user_id][lesson_id]
        
        if progress['start_time'] is None:
            from datetime import datetime
            progress['start_time'] = datetime.now().isoformat()
        
        if completed and step_number not in progress['completed_steps']:
            progress['completed_steps'].append(step_number)
            progress['current_step'] = min(step_number + 1, len(self.lessons[lesson_id]['steps']))
            
            # 检查是否完成整个课程
            if len(progress['completed_steps']) == len(self.lessons[lesson_id]['steps']):
                progress['completed'] = True
                progress['completion_time'] = datetime.now().isoformat()
        
        return progress
    
    def get_next_step(self, user_id, lesson_id):
        """获取用户下一步应该学习的内容"""
        progress = self.get_user_progress(user_id).get(lesson_id)
        if not progress:
            return self.lessons[lesson_id]['steps'][0] if lesson_id in self.lessons else None
        
        current_step = progress['current_step']
        lesson = self.lessons.get(lesson_id)
        
        if lesson and current_step <= len(lesson['steps']):
            return lesson['steps'][current_step - 1]
        
        return None
    
    def check_step_completion(self, user_id, lesson_id, step_number, user_environment):
        """检查用户是否完成了指定步骤的要求"""
        lesson = self.lessons.get(lesson_id)
        if not lesson or step_number > len(lesson['steps']):
            return False, "无效的课程或步骤"
        
        step = lesson['steps'][step_number - 1]
        
        # 根据不同课程和步骤检查完成条件
        if lesson_id == 'lesson2_home_network':
            return self._check_home_network_step(step_number, user_environment)
        elif lesson_id == 'lesson3_dhcp':
            return self._check_dhcp_step(step_number, user_environment)
        elif lesson_id == 'lesson4_nat_port_mapping':
            return self._check_nat_step(step_number, user_environment)
        
        # 默认返回True（对于理论学习步骤）
        return True, "步骤完成"
    
    def _check_home_network_step(self, step_number, env):
        """检查家庭网络搭建步骤"""
        topology = env.get_topology()
        devices = topology['devices']
        connections = topology['connections']
        
        if step_number == 1:  # 添加路由器
            router_count = sum(1 for d in devices.values() if d['type'] == 'router')
            return router_count >= 1, f"需要至少1个路由器，当前{router_count}个"
        
        elif step_number == 2:  # 配置路由器IP
            for device in devices.values():
                if device['type'] == 'router' and device.get('interfaces'):
                    return True, "路由器IP配置完成"
            return False, "路由器需要配置IP地址"
        
        elif step_number == 3:  # 添加家庭设备
            client_count = sum(1 for d in devices.values() if d['type'] == 'client')
            return client_count >= 3, f"需要至少3个客户端，当前{client_count}个"
        
        elif step_number == 4:  # 连接设备
            return len(connections) >= 3, f"需要至少3个连接，当前{len(connections)}个"
        
        elif step_number == 5:  # 配置设备IP
            configured_clients = sum(1 for d in devices.values() 
                                   if d['type'] == 'client' and d.get('interfaces'))
            return configured_clients >= 3, f"需要配置至少3个客户端IP，当前{configured_clients}个"
        
        return True, "步骤完成"
    
    def _check_dhcp_step(self, step_number, env):
        """检查DHCP配置步骤"""
        topology = env.get_topology()
        devices = topology['devices']
        
        if step_number == 1:  # 启用DHCP
            for device_data in devices.values():
                device_id = device_data['id']
                if device_id in env.devices:
                    device = env.devices[device_id]
                    if device.type == 'router' and hasattr(device, 'dhcp_enabled') and device.dhcp_enabled:
                        return True, "DHCP已启用"
            return False, "需要在路由器上启用DHCP"
        
        return True, "步骤完成"
    
    def _check_nat_step(self, step_number, env):
        """检查NAT配置步骤"""
        topology = env.get_topology()
        devices = topology['devices']
        
        if step_number == 3:  # 配置端口映射
            for device_data in devices.values():
                device_id = device_data['id']
                if device_id in env.devices:
                    device = env.devices[device_id]
                    if (device.type == 'router' and hasattr(device, 'port_mappings') 
                        and len(device.port_mappings) > 0):
                        return True, "端口映射已配置"
            return False, "需要配置端口映射规则"
        
        return True, "步骤完成"
