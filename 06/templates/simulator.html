<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络模拟器 - {{ username }}</title>
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <div class="header">
        <h1>网络模拟器</h1>
        <span>用户: {{ username }}</span>
    </div>
    
    <div class="main-container">
        <div class="toolbar">
            <button onclick="addDevice('router')">添加路由器</button>
            <button onclick="addDevice('client')">添加客户端</button>
            <button onclick="clearAll()">清空</button>
            <button onclick="showTemplates()">使用模板</button>
            <button onclick="showScenarios()">练习场景</button>
            <button onclick="showLessons()">教学课程</button>
            <button onclick="showIPLearning()">IP地址学习</button>
            <button onclick="showDiagnostics()">网络诊断</button>
            <button onclick="validateNetwork()">验证网络</button>
        </div>

        <div class="content-area">
            <div class="workspace">
                <div id="canvas" class="canvas"></div>
            </div>

            <div class="sidebar">
            <div class="device-info">
                <h3>设备信息</h3>
                <div id="deviceDetails">选择一个设备查看详情</div>
            </div>
            
            <div class="command-panel">
                <h3>命令执行</h3>
                <select id="deviceSelect">
                    <option value="">选择设备</option>
                </select>
                <input type="text" id="commandInput" placeholder="输入命令 (如: ip a, ip route)">
                <button onclick="executeCommand()">执行</button>
                <div id="commandOutput"></div>
            </div>
            
            <div class="config-panel">
                <h3>接口配置</h3>
                <input type="text" id="interfaceName" placeholder="接口名 (如: eth0)">
                <div class="dhcp-toggle">
                    <label>
                        <input type="checkbox" id="dhcpEnabled" onchange="toggleDHCP()">
                        启用DHCP
                    </label>
                </div>
                <div id="staticConfig">
                    <input type="text" id="ipAddress" placeholder="IP地址">
                    <input type="text" id="subnetMask" placeholder="子网掩码">
                </div>
                <button onclick="configureInterface()">配置接口</button>
            </div>

            <div class="validation-panel">
                <h3>网络验证</h3>
                <div id="validationResults"></div>
            </div>

            <div class="connection-panel">
                <h3>设备连接</h3>
                <p style="font-size: 12px; color: #666; margin-bottom: 10px;">
                    拖拽设备边缘的蓝色圆点来连接设备
                </p>
                <div id="connectionList"></div>
            </div>

            <div class="router-config-panel" style="display: none;">
                <h3>路由器配置</h3>
                <div class="dhcp-config">
                    <h4>DHCP服务</h4>
                    <label>
                        <input type="checkbox" id="dhcpEnable" onchange="toggleDHCPService()">
                        启用DHCP服务
                    </label>
                    <div id="dhcpSettings" style="display: none;">
                        <input type="text" id="dhcpPoolStart" placeholder="地址池开始 (*************)" value="*************">
                        <input type="text" id="dhcpPoolEnd" placeholder="地址池结束 (*************)" value="*************">
                        <button onclick="configureDHCP()">配置DHCP</button>
                    </div>
                </div>
                <div class="port-mapping">
                    <h4>端口映射</h4>
                    <input type="text" id="externalPort" placeholder="外部端口 (8888)">
                    <input type="text" id="internalIP" placeholder="内部IP (*************)">
                    <input type="text" id="internalPort" placeholder="内部端口 (8888)">
                    <button onclick="addPortMapping()">添加映射</button>
                    <div id="portMappingList"></div>
                </div>
            </div>

            <div class="learning-panel" style="display: none;">
                <h3>学习进度</h3>
                <div id="currentLesson"></div>
                <div id="learningProgress"></div>
            </div>
        </div>
        </div>
    </div>

    <div id="templateModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>选择网络模板</h3>
            <div id="templateList"></div>
            <button onclick="closeTemplateModal()">关闭</button>
        </div>
    </div>

    <div id="scenarioModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>练习场景</h3>
            <div id="scenarioList"></div>
            <button onclick="closeScenarioModal()">关闭</button>
        </div>
    </div>

    <div id="connectionModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>创建连接</h3>
            <div id="connectionForm">
                <p>设备1: <span id="device1Name"></span></p>
                <p>设备2: <span id="device2Name"></span></p>
                <label>设备1接口名:</label>
                <input type="text" id="interface1Name" placeholder="如: eth0">
                <label>设备2接口名:</label>
                <input type="text" id="interface2Name" placeholder="如: eth0">
                <button onclick="createConnection()">创建连接</button>
                <button onclick="closeConnectionModal()">取消</button>
            </div>
        </div>
    </div>

    <div id="lessonsModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>教学课程</h3>
            <div id="lessonsList"></div>
            <button onclick="closeLessonsModal()">关闭</button>
        </div>
    </div>

    <div id="ipLearningModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>IP地址学习</h3>
            <div class="ip-classifier">
                <h4>IP地址分类器</h4>
                <input type="text" id="ipInput" placeholder="输入IP地址 (如: ***********)">
                <button onclick="classifyIP()">分析</button>
                <div id="ipClassificationResult"></div>
            </div>
            <div class="ip-quiz">
                <h4>IP地址练习</h4>
                <button onclick="startIPQuiz()">开始练习</button>
                <div id="ipQuizContent"></div>
            </div>
            <button onclick="closeIPLearningModal()">关闭</button>
        </div>
    </div>

    <div id="diagnosticsModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>网络诊断工具</h3>
            <div class="diagnostic-tools">
                <button onclick="runConnectivityTest()">连通性测试</button>
                <button onclick="showNetworkReport()">网络报告</button>
                <div id="diagnosticResults"></div>
            </div>
            <button onclick="closeDiagnosticsModal()">关闭</button>
        </div>
    </div>

    <div id="lessonDetailModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3 id="lessonTitle"></h3>
            <div id="lessonContent">
                <div id="stepContent"></div>
                <div id="stepActions">
                    <button onclick="previousStep()">上一步</button>
                    <button onclick="nextStep()">下一步</button>
                    <button onclick="completeStep()">完成步骤</button>
                </div>
            </div>
            <button onclick="closeLessonDetailModal()">关闭</button>
        </div>
    </div>

    <script src="/static/js/simulator.js"></script>
</body>
</html>
