import random
import time
from datetime import datetime

class NetworkDiagnosticTools:
    def __init__(self):
        self.command_history = []
    
    def execute_ipconfig(self, device, detailed=False):
        """模拟Windows的ipconfig命令"""
        if not device.interfaces:
            return "没有配置网络接口"
        
        result = []
        
        if detailed:
            result.append("Windows IP 配置")
            result.append("")
            
            # 添加主机名和DNS后缀
            result.append(f"   主机名  . . . . . . . . . . . . . : {device.name}")
            result.append("   主 DNS 后缀 . . . . . . . . . . . :")
            result.append("   节点类型  . . . . . . . . . . . . : 混合")
            result.append("   IP 路由已启用 . . . . . . . . . . : 否")
            result.append("   WINS 代理已启用 . . . . . . . . . : 否")
            result.append("")
        
        for interface_name, config in device.interfaces.items():
            if detailed:
                result.append(f"{interface_name} 适配器:")
                result.append("")
                result.append("   连接特定的 DNS 后缀 . . . . . . . :")
                result.append("   描述. . . . . . . . . . . . . . . : 以太网适配器")
                result.append("   物理地址. . . . . . . . . . . . . : " + self._generate_mac_address())
                result.append(f"   DHCP 已启用 . . . . . . . . . . . : {'是' if config.get('dhcp', False) else '否'}")
                result.append("   自动配置已启用. . . . . . . . . . : 是")
                result.append(f"   IPv4 地址 . . . . . . . . . . . . : {config['ip']}(首选)")
                result.append(f"   子网掩码  . . . . . . . . . . . . : {self._mask_to_dotted(config['mask'])}")
                
                # 添加默认网关（如果是客户端）
                if device.type == 'client':
                    result.append("   默认网关. . . . . . . . . . . . . : ***********")
                
                result.append("   DNS 服务器  . . . . . . . . . . . : *******")
                result.append("                                       *******")
                result.append("")
            else:
                result.append(f"{interface_name}:")
                result.append(f"   IPv4 地址 . . . . . . . . . . . . : {config['ip']}")
                result.append(f"   子网掩码  . . . . . . . . . . . . : {self._mask_to_dotted(config['mask'])}")
                if device.type == 'client':
                    result.append("   默认网关. . . . . . . . . . . . . : ***********")
                result.append("")
        
        return '\n'.join(result)
    
    def execute_ifconfig(self, device):
        """模拟Linux/Mac的ifconfig命令"""
        if not device.interfaces:
            return "没有配置网络接口"
        
        result = []
        
        for interface_name, config in device.interfaces.items():
            result.append(f"{interface_name}: flags=4163<UP,BROADCAST,RUNNING,MULTICAST>  mtu 1500")
            result.append(f"        inet {config['ip']}  netmask {self._mask_to_dotted(config['mask'])}  broadcast {self._calculate_broadcast(config['ip'], config['mask'])}")
            result.append(f"        ether {self._generate_mac_address()}  txqueuelen 1000  (Ethernet)")
            result.append("        RX packets 12345  bytes 1234567 (1.2 MB)")
            result.append("        RX errors 0  dropped 0  overruns 0  frame 0")
            result.append("        TX packets 6789  bytes 987654 (987.6 KB)")
            result.append("        TX errors 0  dropped 0 overruns 0  carrier 0  collisions 0")
            result.append("")
        
        # 添加回环接口
        result.append("lo: flags=73<UP,LOOPBACK,RUNNING>  mtu 65536")
        result.append("        inet 127.0.0.1  netmask *********")
        result.append("        loop  txqueuelen 1000  (Local Loopback)")
        result.append("        RX packets 100  bytes 10000 (10.0 KB)")
        result.append("        RX errors 0  dropped 0  overruns 0  frame 0")
        result.append("        TX packets 100  bytes 10000 (10.0 KB)")
        result.append("        TX errors 0  dropped 0 overruns 0  carrier 0  collisions 0")
        
        return '\n'.join(result)
    
    def execute_ip_addr(self, device):
        """模拟Linux的ip addr命令"""
        result = []
        
        # 回环接口
        result.append("1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN group default qlen 1000")
        result.append("    link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00")
        result.append("    inet 127.0.0.1/8 scope host lo")
        result.append("       valid_lft forever preferred_lft forever")
        result.append("")
        
        # 网络接口
        interface_index = 2
        for interface_name, config in device.interfaces.items():
            result.append(f"{interface_index}: {interface_name}: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc pfifo_fast state UP group default qlen 1000")
            result.append(f"    link/ether {self._generate_mac_address()} brd ff:ff:ff:ff:ff:ff")
            result.append(f"    inet {config['ip']}/{config['mask']} brd {self._calculate_broadcast(config['ip'], config['mask'])} scope global {interface_name}")
            result.append("       valid_lft forever preferred_lft forever")
            result.append("")
            interface_index += 1
        
        return '\n'.join(result)
    
    def execute_route_command(self, device):
        """模拟路由表查看命令"""
        result = []
        result.append("内核 IP 路由表")
        result.append("目标            网关            子网掩码        标志  跃点   引用  使用 接口")
        
        # 默认路由
        if device.type == 'client':
            result.append("0.0.0.0         ***********     0.0.0.0         UG    100    0        0 eth0")
        
        # 本地网络路由
        for interface_name, config in device.interfaces.items():
            network = self._calculate_network(config['ip'], config['mask'])
            netmask = self._mask_to_dotted(config['mask'])
            result.append(f"{network:<15} 0.0.0.0         {netmask:<15} U     100    0        0 {interface_name}")
        
        # 本地回环
        result.append("*********       0.0.0.0         *********       U     0      0        0 lo")
        
        return '\n'.join(result)
    
    def execute_enhanced_ping(self, source_device, target_ip, count=4):
        """增强的ping命令，包含教育性解释"""
        from ip_learning_module import IPAddressLearningModule
        ip_module = IPAddressLearningModule()
        
        # 获取源IP（使用第一个接口的IP）
        source_ip = "127.0.0.1"
        if source_device.interfaces:
            source_ip = list(source_device.interfaces.values())[0]['ip']
        
        # 分类IP地址
        target_classification = ip_module.classify_ip_address(target_ip)
        
        result = []
        result.append(f"PING {target_ip} ({target_ip}): 56 data bytes")
        result.append("")
        
        # 添加教育性说明
        result.append(f"📚 学习提示：")
        result.append(f"目标地址 {target_ip} 是 {target_classification['name']}")
        result.append(f"特点：{target_classification['description']}")
        result.append("")
        
        times = []
        for i in range(count):
            delay = ip_module.simulate_ping_delay(source_ip, target_ip)
            times.append(delay)
            result.append(f"64 bytes from {target_ip}: icmp_seq={i+1} time={delay}ms")
            time.sleep(0.1)  # 模拟真实延迟
        
        # 统计信息
        min_time = min(times)
        max_time = max(times)
        avg_time = sum(times) / len(times)
        
        result.append("")
        result.append(f"--- {target_ip} ping statistics ---")
        result.append(f"{count} packets transmitted, {count} received, 0% packet loss")
        result.append(f"round-trip min/avg/max = {min_time:.1f}/{avg_time:.1f}/{max_time:.1f} ms")
        
        # 添加延迟解释
        result.append("")
        result.append("🔍 延迟分析：")
        if target_classification['type'] == 'loopback':
            result.append("本地回环地址延迟极低，因为数据不经过网络接口")
        elif target_classification['type'] == 'private':
            result.append("私有IP地址延迟较低，因为在同一局域网内")
        elif target_classification['type'] == 'public':
            result.append("公网IP地址延迟较高，因为需要经过多个路由器")
        
        return '\n'.join(result)
    
    def network_connectivity_test(self, environment):
        """网络连通性测试"""
        result = []
        result.append("🔍 网络连通性诊断报告")
        result.append("=" * 50)
        result.append("")
        
        devices = environment.devices
        connections = environment.connections
        
        # 设备统计
        router_count = sum(1 for d in devices.values() if d.type == 'router')
        client_count = sum(1 for d in devices.values() if d.type == 'client')
        
        result.append(f"📊 网络设备统计：")
        result.append(f"   路由器数量：{router_count}")
        result.append(f"   客户端数量：{client_count}")
        result.append(f"   连接数量：{len(connections)}")
        result.append("")
        
        # 接口配置检查
        result.append("🔧 接口配置检查：")
        configured_devices = 0
        for device_id, device in devices.items():
            if device.interfaces:
                configured_devices += 1
                result.append(f"   ✅ {device.name} ({device.type}): {len(device.interfaces)} 个接口已配置")
            else:
                result.append(f"   ❌ {device.name} ({device.type}): 未配置接口")
        
        result.append(f"   配置完成度：{configured_devices}/{len(devices)} ({configured_devices/len(devices)*100:.1f}%)")
        result.append("")
        
        # 连通性测试
        result.append("🌐 连通性测试：")
        if len(devices) >= 2:
            device_list = list(devices.values())
            test_pairs = [(device_list[i], device_list[j]) 
                         for i in range(len(device_list)) 
                         for j in range(i+1, len(device_list))]
            
            connected_pairs = 0
            for dev1, dev2 in test_pairs:
                if self._devices_connected(dev1, dev2, connections):
                    result.append(f"   ✅ {dev1.name} ↔ {dev2.name}: 已连接")
                    connected_pairs += 1
                else:
                    result.append(f"   ❌ {dev1.name} ↔ {dev2.name}: 未连接")
            
            result.append(f"   连通率：{connected_pairs}/{len(test_pairs)} ({connected_pairs/len(test_pairs)*100:.1f}%)")
        else:
            result.append("   需要至少2个设备才能进行连通性测试")
        
        result.append("")
        
        # 建议
        result.append("💡 改进建议：")
        if configured_devices < len(devices):
            result.append("   • 为所有设备配置IP地址")
        if router_count == 0:
            result.append("   • 添加至少一个路由器作为网关")
        if len(connections) < len(devices) - 1:
            result.append("   • 增加设备间的连接以提高网络连通性")
        
        return '\n'.join(result)
    
    def _generate_mac_address(self):
        """生成随机MAC地址"""
        return ':'.join([f'{random.randint(0, 255):02x}' for _ in range(6)])
    
    def _mask_to_dotted(self, mask):
        """将CIDR掩码转换为点分十进制"""
        mask_map = {
            '8': '*********',
            '16': '***********',
            '24': '*************',
            '25': '***************',
            '26': '***************',
            '27': '***************',
            '28': '***************',
            '29': '***************',
            '30': '***************'
        }
        return mask_map.get(str(mask), '*************')
    
    def _calculate_broadcast(self, ip, mask):
        """计算广播地址"""
        import ipaddress
        try:
            network = ipaddress.IPv4Network(f"{ip}/{mask}", strict=False)
            return str(network.broadcast_address)
        except:
            return "***************"
    
    def _calculate_network(self, ip, mask):
        """计算网络地址"""
        import ipaddress
        try:
            network = ipaddress.IPv4Network(f"{ip}/{mask}", strict=False)
            return str(network.network_address)
        except:
            return "0.0.0.0"
    
    def _devices_connected(self, dev1, dev2, connections):
        """检查两个设备是否直接连接"""
        for conn in connections:
            if ((conn['device1'] == dev1.id and conn['device2'] == dev2.id) or
                (conn['device1'] == dev2.id and conn['device2'] == dev1.id)):
                return True
        return False
