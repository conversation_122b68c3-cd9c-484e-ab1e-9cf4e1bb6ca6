class PracticeScenarios:
    def __init__(self):
        self.scenarios = {
            'basic': {
                'name': '基础网络连接',
                'description': '创建简单的网络拓扑',
                'tasks': [
                    '添加1个路由器和2个客户端',
                    '为所有设备配置IP地址',
                    '测试设备间的连通性'
                ],
                'expected_devices': {'router': 1, 'client': 2},
                'min_score': 60
            },
            'intermediate': {
                'name': '多子网配置',
                'description': '配置包含多个子网的网络',
                'tasks': [
                    '添加2个路由器和4个客户端',
                    '创建2个不同的子网',
                    '配置路由规则实现跨子网通信',
                    '验证所有设备可以互相通信'
                ],
                'expected_devices': {'router': 2, 'client': 4},
                'min_score': 75
            },
            'advanced': {
                'name': '复杂网络拓扑',
                'description': '设计企业级网络架构',
                'tasks': [
                    '添加3个路由器和6个客户端',
                    '创建3个不同的子网',
                    '配置复杂的路由规则',
                    '实现网络冗余和负载均衡',
                    '测试网络故障恢复能力'
                ],
                'expected_devices': {'router': 3, 'client': 6},
                'min_score': 85
            },
            # L6课程相关场景
            'ip_address_types': {
                'name': 'IP地址类型识别',
                'description': '学习识别不同类型的IP地址',
                'tasks': [
                    '添加1个路由器和3个客户端',
                    '为客户端配置不同类型的IP地址：',
                    '- 一个使用私有IP (192.168.1.x)',
                    '- 一个使用本地回环地址 (127.0.0.1)',
                    '- 一个使用DHCP自动获取IP',
                    '使用ping命令测试不同地址的连通性',
                    '观察本地地址和远程地址的延迟差异'
                ],
                'expected_devices': {'router': 1, 'client': 3},
                'min_score': 70,
                'learning_objectives': [
                    '理解私有IP地址的概念',
                    '认识本地回环地址127.0.0.1的特殊作用',
                    '体验DHCP自动分配IP的过程',
                    '观察不同网络距离的延迟差异'
                ]
            },
            'dhcp_configuration': {
                'name': 'DHCP服务配置',
                'description': '学习路由器的DHCP功能',
                'tasks': [
                    '添加1个路由器和4个客户端',
                    '在路由器上启用DHCP服务',
                    '设置DHCP地址池范围 (*************-*************)',
                    '让客户端通过DHCP自动获取IP地址',
                    '观察路由器的DHCP客户端列表',
                    '尝试为特定设备预留固定IP地址',
                    '测试设备重新连接后IP地址的变化'
                ],
                'expected_devices': {'router': 1, 'client': 4},
                'min_score': 75,
                'learning_objectives': [
                    '理解DHCP服务器的作用',
                    '学会配置DHCP地址池',
                    '体验动态IP分配过程',
                    '了解IP地址预留功能'
                ]
            },
            'home_network_simulation': {
                'name': '家庭网络模拟',
                'description': '模拟真实的家庭网络环境',
                'tasks': [
                    '添加1个路由器（家用路由器）',
                    '添加4个客户端：电脑、手机、平板、智能电视',
                    '路由器配置：',
                    '- 内网IP: ***********',
                    '- DHCP范围: *************-*************',
                    '- 启用NAT功能',
                    '为每个设备分配合适的私有IP地址',
                    '测试所有设备之间的连通性',
                    '模拟访问外网（ping *******）'
                ],
                'expected_devices': {'router': 1, 'client': 4},
                'min_score': 80,
                'learning_objectives': [
                    '理解家庭网络的典型结构',
                    '学习私有IP地址的分配',
                    '体验NAT网络地址转换',
                    '了解路由器作为网关的作用'
                ]
            },
            'port_mapping_experiment': {
                'name': '端口映射实验',
                'description': '学习NAT端口映射功能',
                'tasks': [
                    '搭建家庭网络（1个路由器，3个客户端）',
                    '在一台客户端上"运行"Web服务器（端口8888）',
                    '配置路由器端口映射：外部8888 -> 内部设备8888',
                    '模拟从外网访问内部服务器',
                    '观察NAT地址转换过程',
                    '尝试不同的端口映射配置'
                ],
                'expected_devices': {'router': 1, 'client': 3},
                'min_score': 85,
                'learning_objectives': [
                    '理解端口映射的概念',
                    '学习NAT的工作原理',
                    '体验内网服务对外发布',
                    '了解网络安全的基本概念'
                ]
            },
            'network_troubleshooting': {
                'name': '网络故障排查',
                'description': '学习基本的网络诊断技能',
                'tasks': [
                    '搭建包含故障的网络拓扑',
                    '使用ping命令测试连通性',
                    '使用"ip a"命令查看接口配置',
                    '使用"ip route"命令查看路由表',
                    '识别并修复网络配置问题',
                    '验证修复后的网络连通性'
                ],
                'expected_devices': {'router': 2, 'client': 4},
                'min_score': 80,
                'learning_objectives': [
                    '学习基本的网络诊断命令',
                    '培养网络故障排查思维',
                    '理解网络配置的重要性',
                    '掌握系统性的问题解决方法'
                ]
            }
        }
    
    def get_scenario(self, level):
        return self.scenarios.get(level, None)
    
    def get_all_scenarios(self):
        return self.scenarios
    
    def check_scenario_completion(self, environment, scenario_level):
        scenario = self.get_scenario(scenario_level)
        if not scenario:
            return False, "场景不存在"
        
        topology = environment.get_topology()
        devices = topology['devices']
        
        device_counts = {'router': 0, 'client': 0}
        for device in devices.values():
            device_counts[device['type']] += 1
        
        expected = scenario['expected_devices']
        
        if device_counts['router'] < expected['router']:
            return False, f"路由器数量不足，需要{expected['router']}个，当前{device_counts['router']}个"
        
        if device_counts['client'] < expected['client']:
            return False, f"客户端数量不足，需要{expected['client']}个，当前{device_counts['client']}个"
        
        configured_devices = sum(1 for d in devices.values() if d.get('interfaces'))
        if configured_devices < len(devices) * 0.8:
            return False, "大部分设备需要配置IP地址"
        
        return True, "场景要求已满足"

class NetworkTemplates:
    def __init__(self):
        self.templates = {
            'home_network': {
                'name': '家庭网络',
                'devices': [
                    {'type': 'router', 'name': '家用路由器', 'x': 200, 'y': 150},
                    {'type': 'client', 'name': '电脑', 'x': 100, 'y': 100},
                    {'type': 'client', 'name': '手机', 'x': 300, 'y': 100},
                    {'type': 'client', 'name': '平板', 'x': 200, 'y': 250}
                ],
                'connections': [
                    {'device1': 0, 'device2': 1, 'interface1': 'eth0', 'interface2': 'eth0'},
                    {'device1': 0, 'device2': 2, 'interface1': 'eth1', 'interface2': 'wlan0'},
                    {'device1': 0, 'device2': 3, 'interface1': 'eth2', 'interface2': 'wlan0'}
                ]
            },
            'office_network': {
                'name': '办公室网络',
                'devices': [
                    {'type': 'router', 'name': '核心路由器', 'x': 250, 'y': 150},
                    {'type': 'router', 'name': '部门路由器', 'x': 150, 'y': 250},
                    {'type': 'client', 'name': '服务器', 'x': 350, 'y': 100},
                    {'type': 'client', 'name': '工作站1', 'x': 100, 'y': 300},
                    {'type': 'client', 'name': '工作站2', 'x': 200, 'y': 300},
                    {'type': 'client', 'name': '打印机', 'x': 300, 'y': 250}
                ],
                'connections': [
                    {'device1': 0, 'device2': 1, 'interface1': 'eth0', 'interface2': 'eth0'},
                    {'device1': 0, 'device2': 2, 'interface1': 'eth1', 'interface2': 'eth0'},
                    {'device1': 1, 'device2': 3, 'interface1': 'eth1', 'interface2': 'eth0'},
                    {'device1': 1, 'device2': 4, 'interface1': 'eth2', 'interface2': 'eth0'},
                    {'device1': 0, 'device2': 5, 'interface1': 'eth2', 'interface2': 'eth0'}
                ]
            }
        }
    
    def get_template(self, template_name):
        return self.templates.get(template_name, None)
    
    def get_all_templates(self):
        return list(self.templates.keys())
    
    def apply_template(self, environment, template_name):
        template = self.get_template(template_name)
        if not template:
            return False, "模板不存在"
        
        devices = []
        for device_config in template['devices']:
            device = environment.add_device(
                device_config['type'],
                device_config['name'],
                device_config['x'],
                device_config['y']
            )
            devices.append(device)
        
        for conn in template['connections']:
            device1 = devices[conn['device1']]
            device2 = devices[conn['device2']]
            environment.connect_devices(
                device1.id,
                device2.id,
                conn['interface1'],
                conn['interface2']
            )
        
        return True, f"已应用{template['name']}模板"

class NetworkValidator:
    def __init__(self):
        self.validation_rules = [
            self._check_device_naming,
            self._check_ip_conflicts,
            self._check_subnet_design,
            self._check_connectivity,
            self._check_routing_logic
        ]
    
    def validate_network(self, environment):
        topology = environment.get_topology()
        issues = []
        warnings = []
        
        for rule in self.validation_rules:
            rule_issues, rule_warnings = rule(topology)
            issues.extend(rule_issues)
            warnings.extend(rule_warnings)
        
        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'warnings': warnings,
            'score_impact': len(issues) * -5 + len(warnings) * -2
        }
    
    def _check_device_naming(self, topology):
        issues = []
        warnings = []
        
        device_names = [d['name'] for d in topology['devices'].values()]
        if len(device_names) != len(set(device_names)):
            issues.append("设备名称重复")
        
        for device in topology['devices'].values():
            if not device['name'] or device['name'].strip() == '':
                warnings.append(f"设备{device['id']}缺少名称")
        
        return issues, warnings
    
    def _check_ip_conflicts(self, topology):
        issues = []
        warnings = []
        
        used_ips = set()
        for device in topology['devices'].values():
            for interface, config in device.get('interfaces', {}).items():
                ip = config.get('ip')
                if ip:
                    if ip in used_ips:
                        issues.append(f"IP地址冲突: {ip}")
                    used_ips.add(ip)
        
        return issues, warnings
    
    def _check_subnet_design(self, topology):
        issues = []
        warnings = []
        
        subnets = set()
        for device in topology['devices'].values():
            for interface, config in device.get('interfaces', {}).items():
                ip = config.get('ip')
                mask = config.get('mask')
                if ip and mask:
                    try:
                        import ipaddress
                        if mask.isdigit():
                            network = ipaddress.IPv4Network(f"{ip}/{mask}", strict=False)
                        else:
                            network = ipaddress.IPv4Network(f"{ip}/{mask}", strict=False)
                        subnets.add(str(network.network_address))
                    except:
                        warnings.append(f"无效的IP配置: {ip}/{mask}")
        
        if len(subnets) < 2 and len(topology['devices']) > 3:
            warnings.append("建议使用多个子网来组织网络")
        
        return issues, warnings
    
    def _check_connectivity(self, topology):
        issues = []
        warnings = []
        
        if len(topology['connections']) == 0 and len(topology['devices']) > 1:
            issues.append("设备之间缺少连接")
        
        connected_devices = set()
        for conn in topology['connections']:
            connected_devices.add(conn['device1'])
            connected_devices.add(conn['device2'])
        
        isolated_devices = set(topology['devices'].keys()) - connected_devices
        if isolated_devices:
            warnings.append(f"存在孤立设备: {len(isolated_devices)}个")
        
        return issues, warnings
    
    def _check_routing_logic(self, topology):
        issues = []
        warnings = []
        
        router_count = sum(1 for d in topology['devices'].values() if d['type'] == 'router')
        client_count = sum(1 for d in topology['devices'].values() if d['type'] == 'client')
        
        if router_count == 0 and client_count > 1:
            issues.append("多个客户端需要路由器进行连接")
        
        if router_count > client_count and client_count > 0:
            warnings.append("路由器数量可能过多")
        
        return issues, warnings
