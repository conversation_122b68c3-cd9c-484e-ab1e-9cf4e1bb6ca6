from flask import Flask, render_template, request, jsonify, session, redirect, Response
import uuid
import json
import threading
import time
from datetime import datetime
from auto_grader import NetworkGrader, TeacherDashboard
from practice_scenarios import PracticeScenarios, NetworkTemplates, NetworkValidator
from ip_learning_module import IPAddressLearningModule
from network_diagnostic_tools import NetworkDiagnosticTools
from teaching_guide_system import TeachingGuideSystem

app = Flask(__name__)
app.secret_key = 'network_sim_secret_key'

class NetworkDevice:
    def __init__(self, device_type, name, x=0, y=0):
        self.id = str(uuid.uuid4())
        self.type = device_type
        self.name = name
        self.x = x
        self.y = y
        self.interfaces = {}
        self.routing_table = []
        self.connected_devices = {}

        # 路由器特有功能
        if device_type == 'router':
            self.dhcp_enabled = False
            self.dhcp_pool_start = "*************"
            self.dhcp_pool_end = "*************"
            self.dhcp_clients = {}  # MAC -> IP映射
            self.dhcp_reservations = {}  # MAC -> 预留IP
            self.nat_enabled = True
            self.port_mappings = []  # 端口映射规则
            self.public_ip = "***********"  # 模拟公网IP
        
    def add_interface(self, interface_name, ip_address, subnet_mask, dhcp=False):
        if dhcp:
            # 模拟DHCP分配IP
            import random
            base_ip = f"192.168.{random.randint(1, 254)}.{random.randint(2, 254)}"
            self.interfaces[interface_name] = {
                'ip': base_ip,
                'mask': '24',
                'dhcp': True,
                'status': 'up'
            }
        else:
            self.interfaces[interface_name] = {
                'ip': ip_address,
                'mask': subnet_mask,
                'dhcp': False,
                'status': 'up'
            }
        
    def connect_to(self, other_device, interface1, interface2):
        self.connected_devices[interface1] = {
            'device_id': other_device.id,
            'interface': interface2
        }
        other_device.connected_devices[interface2] = {
            'device_id': self.id,
            'interface': interface1
        }
        
    def execute_command(self, command, diagnostic_tools=None):
        if command == 'ip a' or command == 'ip addr':
            if diagnostic_tools:
                return diagnostic_tools.execute_ip_addr(self)
            return self._show_interfaces()
        elif command == 'ip route':
            if diagnostic_tools:
                return diagnostic_tools.execute_route_command(self)
            return self._show_routing_table()
        elif command == 'ipconfig':
            if diagnostic_tools:
                return diagnostic_tools.execute_ipconfig(self, detailed=False)
            return self._show_interfaces()
        elif command == 'ipconfig /all':
            if diagnostic_tools:
                return diagnostic_tools.execute_ipconfig(self, detailed=True)
            return self._show_interfaces()
        elif command == 'ifconfig':
            if diagnostic_tools:
                return diagnostic_tools.execute_ifconfig(self)
            return self._show_interfaces()
        elif command.startswith('ping '):
            target = command.split(' ')[1]
            if diagnostic_tools:
                return diagnostic_tools.execute_enhanced_ping(self, target)
            return self._ping(target)
        else:
            return f"Command '{command}' not recognized\n\n支持的命令:\n- ip a / ip addr\n- ip route\n- ipconfig [/all]\n- ifconfig\n- ping <IP地址>"
            
    def _show_interfaces(self):
        result = []
        for iface, config in self.interfaces.items():
            result.append(f"{iface}: {config['ip']}/{config['mask']} {config['status']}")
        return '\n'.join(result) if result else "No interfaces configured"
        
    def _show_routing_table(self):
        if not self.routing_table:
            return "No routes configured"
        result = []
        for route in self.routing_table:
            result.append(f"{route['destination']} via {route['gateway']} dev {route['interface']}")
        return '\n'.join(result)
        
    def _ping(self, target):
        import random

        # 检查是否是本地地址
        is_local = self._is_local_address(target)

        results = []
        for i in range(4):
            if is_local:
                # 本地地址：0.1-2.0ms
                time_ms = round(random.uniform(0.1, 2.0), 1)
            else:
                # 远程地址：1.0-100.0ms
                time_ms = round(random.uniform(1.0, 100.0), 1)

            seq = i + 1
            results.append(f"64 bytes from {target}: icmp_seq={seq} time={time_ms}ms")

        ping_header = f"PING {target} ({target}): 56 data bytes"
        ping_results = '\n'.join(results)

        # 计算统计信息
        times = [float(line.split('time=')[1].split('ms')[0]) for line in results]
        min_time = min(times)
        max_time = max(times)
        avg_time = sum(times) / len(times)

        stats = f"\n--- {target} ping statistics ---\n4 packets transmitted, 4 received, 0% packet loss\nround-trip min/avg/max = {min_time:.1f}/{avg_time:.1f}/{max_time:.1f} ms"

        return f"{ping_header}\n{ping_results}{stats}"

    def _is_local_address(self, target):
        # 检查是否是本机接口地址或localhost
        if target == '127.0.0.1' or target == 'localhost':
            return True

        # 检查是否是本设备的接口地址
        for interface, config in self.interfaces.items():
            if config['ip'] == target:
                return True

        return False

    def enable_dhcp(self, pool_start="*************", pool_end="*************"):
        """启用DHCP服务器"""
        if self.type == 'router':
            self.dhcp_enabled = True
            self.dhcp_pool_start = pool_start
            self.dhcp_pool_end = pool_end
            return True
        return False

    def disable_dhcp(self):
        """禁用DHCP服务器"""
        if self.type == 'router':
            self.dhcp_enabled = False
            self.dhcp_clients.clear()
            return True
        return False

    def assign_dhcp_ip(self, client_mac):
        """为客户端分配DHCP IP地址"""
        if not self.dhcp_enabled or self.type != 'router':
            return None

        # 检查是否有预留地址
        if client_mac in self.dhcp_reservations:
            ip = self.dhcp_reservations[client_mac]
            self.dhcp_clients[client_mac] = ip
            return ip

        # 检查是否已经分配过
        if client_mac in self.dhcp_clients:
            return self.dhcp_clients[client_mac]

        # 分配新的IP地址
        import ipaddress
        start_ip = ipaddress.IPv4Address(self.dhcp_pool_start)
        end_ip = ipaddress.IPv4Address(self.dhcp_pool_end)

        assigned_ips = set(self.dhcp_clients.values())

        for ip_int in range(int(start_ip), int(end_ip) + 1):
            ip = str(ipaddress.IPv4Address(ip_int))
            if ip not in assigned_ips:
                self.dhcp_clients[client_mac] = ip
                return ip

        return None  # 地址池已满

    def add_dhcp_reservation(self, client_mac, reserved_ip):
        """添加DHCP地址预留"""
        if self.type == 'router':
            self.dhcp_reservations[client_mac] = reserved_ip
            return True
        return False

    def add_port_mapping(self, external_port, internal_ip, internal_port, protocol="TCP"):
        """添加端口映射规则"""
        if self.type == 'router':
            mapping = {
                'external_port': external_port,
                'internal_ip': internal_ip,
                'internal_port': internal_port,
                'protocol': protocol.upper()
            }
            self.port_mappings.append(mapping)
            return True
        return False

    def remove_port_mapping(self, external_port):
        """移除端口映射规则"""
        if self.type == 'router':
            self.port_mappings = [m for m in self.port_mappings
                                if m['external_port'] != external_port]
            return True
        return False

    def get_dhcp_status(self):
        """获取DHCP服务状态"""
        if self.type != 'router':
            return None

        return {
            'enabled': self.dhcp_enabled,
            'pool_start': self.dhcp_pool_start,
            'pool_end': self.dhcp_pool_end,
            'clients': self.dhcp_clients.copy(),
            'reservations': self.dhcp_reservations.copy()
        }

    def get_nat_status(self):
        """获取NAT配置状态"""
        if self.type != 'router':
            return None

        return {
            'enabled': self.nat_enabled,
            'public_ip': self.public_ip,
            'port_mappings': self.port_mappings.copy()
        }

class NetworkEnvironment:
    def __init__(self, user_id):
        self.user_id = user_id
        self.devices = {}
        self.connections = []
        self.created_at = datetime.now()
        
    def add_device(self, device_type, name, x=0, y=0):
        device = NetworkDevice(device_type, name, x, y)
        self.devices[device.id] = device
        return device
        
    def remove_device(self, device_id):
        if device_id in self.devices:
            del self.devices[device_id]
            self.connections = [conn for conn in self.connections 
                             if conn['device1'] != device_id and conn['device2'] != device_id]
            
    def connect_devices(self, device1_id, device2_id, interface1, interface2):
        if device1_id in self.devices and device2_id in self.devices:
            device1 = self.devices[device1_id]
            device2 = self.devices[device2_id]
            device1.connect_to(device2, interface1, interface2)
            self.connections.append({
                'device1': device1_id,
                'device2': device2_id,
                'interface1': interface1,
                'interface2': interface2
            })

    def disconnect_devices(self, device1_id, device2_id, interface1, interface2):
        if device1_id in self.devices and device2_id in self.devices:
            device1 = self.devices[device1_id]
            device2 = self.devices[device2_id]

            # 移除设备间的连接
            if interface1 in device1.connected_devices:
                del device1.connected_devices[interface1]
            if interface2 in device2.connected_devices:
                del device2.connected_devices[interface2]

            # 从连接列表中移除
            self.connections = [conn for conn in self.connections
                             if not ((conn['device1'] == device1_id and conn['device2'] == device2_id and
                                     conn['interface1'] == interface1 and conn['interface2'] == interface2) or
                                    (conn['device1'] == device2_id and conn['device2'] == device1_id and
                                     conn['interface1'] == interface2 and conn['interface2'] == interface1))]
            
    def get_topology(self):
        return {
            'devices': {dev_id: {
                'id': dev.id,
                'type': dev.type,
                'name': dev.name,
                'x': dev.x,
                'y': dev.y,
                'interfaces': dev.interfaces
            } for dev_id, dev in self.devices.items()},
            'connections': self.connections
        }

user_environments = {}
teacher_dashboard_instance = TeacherDashboard(user_environments)
practice_scenarios = PracticeScenarios()
network_templates = NetworkTemplates()
network_validator = NetworkValidator()
ip_learning_module = IPAddressLearningModule()
diagnostic_tools = NetworkDiagnosticTools()
teaching_guide = TeachingGuideSystem()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/login', methods=['POST'])
def login():
    username = request.json.get('username')
    if username:
        session['username'] = username
        user_id = f"user_{username}"
        if user_id not in user_environments:
            user_environments[user_id] = NetworkEnvironment(user_id)
        return jsonify({'success': True, 'username': username})
    return jsonify({'success': False})

@app.route('/simulator')
def simulator():
    if 'username' not in session:
        return redirect('/')
    return render_template('simulator.html', username=session['username'])

@app.route('/api/add_device', methods=['POST'])
def add_device():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    if user_id not in user_environments:
        user_environments[user_id] = NetworkEnvironment(user_id)

    env = user_environments[user_id]

    data = request.json
    device = env.add_device(data['type'], data['name'], data.get('x', 0), data.get('y', 0))

    return jsonify({
        'success': True,
        'device': {
            'id': device.id,
            'type': device.type,
            'name': device.name,
            'x': device.x,
            'y': device.y
        }
    })

@app.route('/api/execute_command', methods=['POST'])
def execute_command():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    if user_id not in user_environments:
        user_environments[user_id] = NetworkEnvironment(user_id)

    env = user_environments[user_id]

    data = request.json
    device_id = data['device_id']
    command = data['command']

    if device_id in env.devices:
        result = env.devices[device_id].execute_command(command, diagnostic_tools)
        return jsonify({'success': True, 'output': result})

    return jsonify({'error': 'Device not found'})

@app.route('/api/topology')
def get_topology():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    if user_id not in user_environments:
        user_environments[user_id] = NetworkEnvironment(user_id)

    env = user_environments[user_id]

    return jsonify(env.get_topology())

@app.route('/api/configure_interface', methods=['POST'])
def configure_interface():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    if user_id not in user_environments:
        user_environments[user_id] = NetworkEnvironment(user_id)

    env = user_environments[user_id]

    data = request.json
    device_id = data['device_id']
    interface = data['interface']
    ip_address = data['ip_address']
    subnet_mask = data['subnet_mask']
    dhcp = data.get('dhcp', False)

    if device_id in env.devices:
        env.devices[device_id].add_interface(interface, ip_address, subnet_mask, dhcp)

        # 如果是DHCP，返回分配的IP地址
        if dhcp:
            actual_config = env.devices[device_id].interfaces[interface]
            return jsonify({
                'success': True,
                'ip_address': actual_config['ip'],
                'subnet_mask': actual_config['mask']
            })
        else:
            return jsonify({'success': True})

    return jsonify({'error': 'Device not found'})

@app.route('/api/connect_devices', methods=['POST'])
def connect_devices():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    if user_id not in user_environments:
        user_environments[user_id] = NetworkEnvironment(user_id)

    env = user_environments[user_id]

    data = request.json
    device1_id = data['device1_id']
    device2_id = data['device2_id']
    interface1 = data['interface1']
    interface2 = data['interface2']

    if device1_id in env.devices and device2_id in env.devices:
        env.connect_devices(device1_id, device2_id, interface1, interface2)
        return jsonify({'success': True})

    return jsonify({'error': 'One or both devices not found'})

@app.route('/api/disconnect_devices', methods=['POST'])
def disconnect_devices():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    if user_id not in user_environments:
        user_environments[user_id] = NetworkEnvironment(user_id)

    env = user_environments[user_id]

    data = request.json
    device1_id = data['device1_id']
    device2_id = data['device2_id']
    interface1 = data['interface1']
    interface2 = data['interface2']

    if device1_id in env.devices and device2_id in env.devices:
        env.disconnect_devices(device1_id, device2_id, interface1, interface2)
        return jsonify({'success': True})

    return jsonify({'error': 'One or both devices not found'})

@app.route('/teacher')
def teacher_dashboard():
    return render_template('teacher.html')

@app.route('/api/teacher/scores')
def get_all_scores():
    scores = teacher_dashboard_instance.get_all_scores()
    return jsonify(scores)

@app.route('/api/teacher/score/<username>')
def get_user_score(username):
    score = teacher_dashboard_instance.get_user_score(username)
    if score:
        return jsonify(score)
    return jsonify({'error': 'User not found'})

@app.route('/api/teacher/export')
def export_scores():
    csv_data = teacher_dashboard_instance.export_scores_csv()
    return Response(
        csv_data,
        mimetype='text/csv',
        headers={'Content-Disposition': 'attachment; filename=network_scores.csv'}
    )

@app.route('/api/scenarios')
def get_scenarios():
    return jsonify(practice_scenarios.get_all_scenarios())

@app.route('/api/scenario/<level>/check')
def check_scenario(level):
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    if user_id not in user_environments:
        user_environments[user_id] = NetworkEnvironment(user_id)

    env = user_environments[user_id]

    success, message = practice_scenarios.check_scenario_completion(env, level)
    return jsonify({'success': success, 'message': message})

@app.route('/api/templates')
def get_templates():
    return jsonify({
        'templates': network_templates.get_all_templates(),
        'details': {name: network_templates.get_template(name)
                   for name in network_templates.get_all_templates()}
    })

@app.route('/api/template/<template_name>/apply', methods=['POST'])
def apply_template(template_name):
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    if user_id not in user_environments:
        user_environments[user_id] = NetworkEnvironment(user_id)

    env = user_environments[user_id]

    success, message = network_templates.apply_template(env, template_name)
    return jsonify({'success': success, 'message': message})

@app.route('/api/validate')
def validate_network():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    if user_id not in user_environments:
        user_environments[user_id] = NetworkEnvironment(user_id)

    env = user_environments[user_id]

    validation_result = network_validator.validate_network(env)
    return jsonify(validation_result)

# DHCP和NAT配置API
@app.route('/api/configure_dhcp', methods=['POST'])
def configure_dhcp():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    if user_id not in user_environments:
        return jsonify({'error': 'Environment not found'})

    env = user_environments[user_id]
    data = request.json
    device_id = data.get('device_id')
    enabled = data.get('enabled', False)
    pool_start = data.get('pool_start', '*************')
    pool_end = data.get('pool_end', '*************')

    if device_id in env.devices:
        device = env.devices[device_id]
        if enabled:
            success = device.enable_dhcp(pool_start, pool_end)
        else:
            success = device.disable_dhcp()

        if success:
            return jsonify({'success': True, 'dhcp_status': device.get_dhcp_status()})
        else:
            return jsonify({'error': 'Device is not a router'})

    return jsonify({'error': 'Device not found'})

@app.route('/api/add_port_mapping', methods=['POST'])
def add_port_mapping():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    if user_id not in user_environments:
        return jsonify({'error': 'Environment not found'})

    env = user_environments[user_id]
    data = request.json
    device_id = data.get('device_id')
    external_port = data.get('external_port')
    internal_ip = data.get('internal_ip')
    internal_port = data.get('internal_port')
    protocol = data.get('protocol', 'TCP')

    if device_id in env.devices:
        device = env.devices[device_id]
        success = device.add_port_mapping(external_port, internal_ip, internal_port, protocol)

        if success:
            return jsonify({'success': True, 'nat_status': device.get_nat_status()})
        else:
            return jsonify({'error': 'Device is not a router'})

    return jsonify({'error': 'Device not found'})

@app.route('/api/get_router_config/<device_id>')
def get_router_config(device_id):
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    if user_id not in user_environments:
        return jsonify({'error': 'Environment not found'})

    env = user_environments[user_id]

    if device_id in env.devices:
        device = env.devices[device_id]
        if device.type == 'router':
            return jsonify({
                'dhcp_status': device.get_dhcp_status(),
                'nat_status': device.get_nat_status()
            })
        else:
            return jsonify({'error': 'Device is not a router'})

    return jsonify({'error': 'Device not found'})

@app.route('/api/dhcp_assign_ip', methods=['POST'])
def dhcp_assign_ip():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    if user_id not in user_environments:
        return jsonify({'error': 'Environment not found'})

    env = user_environments[user_id]
    data = request.json
    router_id = data.get('router_id')
    client_mac = data.get('client_mac', 'auto-generated')

    if router_id in env.devices:
        router = env.devices[router_id]
        if router.type == 'router':
            # 生成模拟MAC地址
            if client_mac == 'auto-generated':
                import random
                client_mac = ':'.join([f'{random.randint(0, 255):02x}' for _ in range(6)])

            assigned_ip = router.assign_dhcp_ip(client_mac)
            if assigned_ip:
                return jsonify({
                    'success': True,
                    'assigned_ip': assigned_ip,
                    'client_mac': client_mac,
                    'dhcp_status': router.get_dhcp_status()
                })
            else:
                return jsonify({'error': 'DHCP pool exhausted'})
        else:
            return jsonify({'error': 'Device is not a router'})

    return jsonify({'error': 'Router not found'})

# IP地址学习模块API
@app.route('/api/ip_learning/classify', methods=['POST'])
def classify_ip_address():
    data = request.json
    ip_address = data.get('ip_address', '')

    classification = ip_learning_module.classify_ip_address(ip_address)
    return jsonify(classification)

@app.route('/api/ip_learning/quiz')
def get_ip_quiz():
    count = request.args.get('count', 5, type=int)
    questions = ip_learning_module.generate_quiz_questions(count)
    return jsonify({'questions': questions})

@app.route('/api/ip_learning/content')
def get_learning_content():
    content = ip_learning_module.get_learning_content()
    return jsonify(content)

@app.route('/api/ip_learning/ping_simulation', methods=['POST'])
def simulate_ping():
    data = request.json
    source_ip = data.get('source_ip', '*************')
    target_ip = data.get('target_ip', '127.0.0.1')

    delay = ip_learning_module.simulate_ping_delay(source_ip, target_ip)
    source_type = ip_learning_module.classify_ip_address(source_ip)
    target_type = ip_learning_module.classify_ip_address(target_ip)

    return jsonify({
        'source_ip': source_ip,
        'target_ip': target_ip,
        'delay_ms': delay,
        'source_type': source_type,
        'target_type': target_type,
        'explanation': f"从{source_type['name']}到{target_type['name']}的延迟通常为{delay}ms"
    })

# 网络诊断工具API
@app.route('/api/diagnostic/connectivity_test')
def network_connectivity_test():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    if user_id not in user_environments:
        return jsonify({'error': 'Environment not found'})

    env = user_environments[user_id]
    report = diagnostic_tools.network_connectivity_test(env)

    return jsonify({'report': report})

@app.route('/api/diagnostic/device_info/<device_id>')
def get_device_diagnostic_info(device_id):
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    if user_id not in user_environments:
        return jsonify({'error': 'Environment not found'})

    env = user_environments[user_id]

    if device_id in env.devices:
        device = env.devices[device_id]

        info = {
            'basic_info': {
                'name': device.name,
                'type': device.type,
                'id': device.id,
                'position': {'x': device.x, 'y': device.y}
            },
            'interfaces': device.interfaces,
            'connections': device.connected_devices,
            'ipconfig_output': diagnostic_tools.execute_ipconfig(device, detailed=True),
            'ifconfig_output': diagnostic_tools.execute_ifconfig(device),
            'ip_addr_output': diagnostic_tools.execute_ip_addr(device),
            'route_output': diagnostic_tools.execute_route_command(device)
        }

        # 如果是路由器，添加路由器特有信息
        if device.type == 'router':
            info['router_config'] = {
                'dhcp_status': device.get_dhcp_status(),
                'nat_status': device.get_nat_status()
            }

        return jsonify(info)

    return jsonify({'error': 'Device not found'})

# 教学引导系统API
@app.route('/api/teaching/lessons')
def get_lesson_list():
    lessons = teaching_guide.get_lesson_list()
    return jsonify({'lessons': lessons})

@app.route('/api/teaching/lesson/<lesson_id>')
def get_lesson_content(lesson_id):
    lesson = teaching_guide.get_lesson_content(lesson_id)
    if lesson:
        return jsonify({'lesson': lesson})
    return jsonify({'error': 'Lesson not found'})

@app.route('/api/teaching/progress')
def get_user_progress():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    progress = teaching_guide.get_user_progress(user_id)
    return jsonify({'progress': progress})

@app.route('/api/teaching/next_step/<lesson_id>')
def get_next_step(lesson_id):
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    next_step = teaching_guide.get_next_step(user_id, lesson_id)

    if next_step:
        return jsonify({'step': next_step})
    return jsonify({'error': 'No next step available'})

@app.route('/api/teaching/complete_step', methods=['POST'])
def complete_step():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    data = request.json
    lesson_id = data.get('lesson_id')
    step_number = data.get('step_number')

    if user_id not in user_environments:
        return jsonify({'error': 'Environment not found'})

    env = user_environments[user_id]

    # 检查步骤完成条件
    completed, message = teaching_guide.check_step_completion(user_id, lesson_id, step_number, env)

    if completed:
        progress = teaching_guide.update_user_progress(user_id, lesson_id, step_number, completed=True)
        return jsonify({
            'success': True,
            'message': message,
            'progress': progress
        })
    else:
        return jsonify({
            'success': False,
            'message': message
        })

@app.route('/api/teaching/check_step/<lesson_id>/<int:step_number>')
def check_step_completion(lesson_id, step_number):
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"

    if user_id not in user_environments:
        return jsonify({'error': 'Environment not found'})

    env = user_environments[user_id]
    completed, message = teaching_guide.check_step_completion(user_id, lesson_id, step_number, env)

    return jsonify({
        'completed': completed,
        'message': message
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080, debug=True)
